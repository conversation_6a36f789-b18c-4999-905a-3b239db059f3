# 漫画翻译助手 Chrome 插件

一款帮助翻译外文漫画的Chrome插件，能够保持原有文字样式，提供沉浸式阅读体验。

## 功能特点

- 自动检测漫画页面中的文字区域
- 提取文字内容并进行翻译
- 将翻译后的文字以接近原样式的方式覆盖在原文上
- 支持多语言翻译
- 提供翻译结果的调整和优化选项
- 支持手动和自动翻译模式
- 缓存翻译结果，提高效率

## 安装方法

### 从Chrome网上应用店安装

1. 访问Chrome网上应用店（即将上线）
2. 点击"添加到Chrome"按钮

### 手动安装开发版

1. 下载本仓库代码
2. 打开Chrome浏览器，进入扩展程序页面 (chrome://extensions/)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本仓库中的`dist`目录

## 使用方法

### 基本设置

1. 点击浏览器工具栏中的插件图标，打开弹出窗口
2. 输入您的OpenAI API密钥
3. 选择目标翻译语言
4. 启用翻译功能
5. 选择翻译模式（手动/自动）

### 手动模式

- 浏览漫画页面时，点击需要翻译的图像
- 插件会自动检测文字区域并进行翻译
- 翻译完成后，可以通过控制按钮切换原图/翻译视图

### 自动模式

- 浏览漫画页面时，插件会自动检测并翻译页面上的所有漫画图像
- 可以通过控制按钮切换原图/翻译视图

### 快捷键

- Alt+T: 启用/禁用翻译功能
- Alt+S: 翻译选中的图像区域

## 高级设置

点击弹出窗口中的"高级设置"按钮，可以访问更多设置选项：

- API设置：选择模型、调整温度等参数
- 样式设置：自定义字体、大小、颜色等
- 快捷键：自定义快捷键
- 缓存管理：管理翻译缓存
- 高级设置：调试模式、图像预处理等选项

## 隐私说明

- 您的API密钥仅存储在本地，不会发送到任何第三方服务器
- 图像数据仅用于文字识别和翻译，不会用于其他目的
- 翻译结果缓存在本地浏览器中，不会上传到云端

## 技术支持

如有问题或建议，请通过以下方式联系我们：

- 提交GitHub Issue
- 发送邮件至：[您的邮箱]

## 许可证

MIT License

## 最新改进（安全与资源管理优化）

### 配置管理

- 创建了配置的Single Source of Truth（`default-config.js`），集中管理所有默认配置
- 实现了基于观察者模式的配置管理器（`config-manager.js`），确保配置一致性
- 移除了硬编码的API密钥，提升了安全性
- 添加了首次使用引导，指导用户设置API密钥

### API提供者架构

- 重构了API提供者架构，实现更灵活的多提供者支持
- 改进了API提供者基类，添加了资源注册与释放机制
- 标准化了错误处理流程，提高了系统稳定性

### OCR改进

- 增强了OCR提供者基类，添加了资源管理功能
- 改进了Tesseract提供者实现，确保资源正确释放
- 实现了OCR提供者的并行终止机制，提高了资源回收效率
- 添加了配置变更检测，自动重新初始化OCR资源

### 安全性改进

- 替换了默认API密钥为空值，要求用户提供自己的密钥
- 优化了API密钥输入组件，添加了基本的格式验证
- 添加了安全提示，说明密钥的存储和使用方式

### 用户体验优化

- 添加了加载状态指示器，提高用户体验
- 优化了配置界面，添加了更详细的选项描述
- 实现了配置重置功能，允许用户轻松恢复默认设置

这些改进显著提升了插件的安全性、稳定性和资源管理能力，为后续功能扩展奠定了坚实基础。
