# 漫画翻译Chrome插件技术实现文档

## 1. 项目结构

```
chrome-plugin-manga-translator/
├── public/                 # 静态资源
│   ├── icons/              # 插件图标
│   └── manifest.json       # 插件配置文件
├── src/                    # 源代码
│   ├── components/         # React组件
│   │   ├── Popup/          # 弹出窗口组件
│   │   └── Options/        # 选项页面组件
│   ├── content/            # 内容脚本
│   │   ├── detector.js     # 文字检测模块
│   │   ├── translator.js   # 翻译模块
│   │   ├── renderer.js     # 渲染模块
│   │   └── content.jsx     # 内容脚本入口
│   ├── background/         # 后台脚本
│   │   └── background.js   # 后台脚本入口
│   ├── utils/              # 工具函数
│   │   ├── api.js          # API调用封装
│   │   ├── storage.js      # 存储相关函数
│   │   └── imageProcess.js # 图像处理函数
│   ├── popup.jsx           # 弹出窗口入口
│   └── options.jsx         # 选项页面入口
├── dist/                   # 构建输出目录
├── node_modules/           # 依赖包
├── package.json            # 项目配置
├── vite.config.js          # Vite配置
└── README.md               # 项目说明
```

## 2. 核心模块详细设计

### 2.1 文字检测模块 (detector.js)

#### 功能描述
负责识别漫画图像中的文字区域，提取文字内容。

#### 主要API
```javascript
/**
 * 检测图像中的文字区域
 * @param {HTMLImageElement} image - 需要处理的图像元素
 * @returns {Promise<Array>} - 返回检测到的文字区域信息数组
 */
async function detectTextAreas(image) { ... }

/**
 * 提取文字区域的内容
 * @param {HTMLImageElement} image - 图像元素
 * @param {Object} textArea - 文字区域信息
 * @returns {Promise<string>} - 返回提取的文字内容
 */
async function extractText(image, textArea) { ... }
```

#### 实现思路
1. 使用Canvas API获取图像数据
2. 将图像数据发送到OpenAI Vision API
3. 解析API返回的结果，提取文字区域坐标和内容
4. 返回结构化的文字区域信息

### 2.2 翻译模块 (translator.js)

#### 功能描述
负责将检测到的文字内容翻译成目标语言。

#### 主要API
```javascript
/**
 * 翻译文本内容
 * @param {string} text - 需要翻译的文本
 * @param {string} targetLang - 目标语言代码
 * @param {Object} options - 翻译选项
 * @returns {Promise<string>} - 返回翻译后的文本
 */
async function translateText(text, targetLang, options = {}) { ... }

/**
 * 批量翻译多个文本
 * @param {Array<string>} texts - 需要翻译的文本数组
 * @param {string} targetLang - 目标语言代码
 * @param {Object} options - 翻译选项
 * @returns {Promise<Array<string>>} - 返回翻译后的文本数组
 */
async function batchTranslate(texts, targetLang, options = {}) { ... }
```

#### 实现思路
1. 根据用户配置选择翻译API（默认OpenAI）
2. 构建API请求参数，包括源文本、目标语言等
3. 发送请求并处理响应
4. 返回翻译结果

### 2.3 渲染模块 (renderer.js)

#### 功能描述
负责将翻译后的文字以接近原样式的方式覆盖在原图像上。

#### 主要API
```javascript
/**
 * 在图像上渲染翻译后的文字
 * @param {HTMLImageElement} image - 原始图像元素
 * @param {Array} textAreas - 文字区域信息数组
 * @param {Array<string>} translatedTexts - 翻译后的文本数组
 * @param {Object} styleOptions - 样式选项
 * @returns {HTMLCanvasElement} - 返回处理后的Canvas元素
 */
function renderTranslatedImage(image, textAreas, translatedTexts, styleOptions = {}) { ... }

/**
 * 分析原文字样式
 * @param {HTMLImageElement} image - 图像元素
 * @param {Object} textArea - 文字区域信息
 * @returns {Object} - 返回分析的样式信息
 */
function analyzeTextStyle(image, textArea) { ... }
```

#### 实现思路
1. 创建Canvas元素并绘制原图像
2. 分析原文字的样式特征（颜色、大小、方向等）
3. 在文字区域绘制半透明背景
4. 使用分析的样式参数绘制翻译后的文字
5. 返回处理后的Canvas元素

### 2.4 存储模块 (storage.js)

#### 功能描述
负责管理用户配置和缓存数据。

#### 主要API
```javascript
/**
 * 保存用户配置
 * @param {Object} config - 用户配置对象
 * @returns {Promise<void>}
 */
async function saveConfig(config) { ... }

/**
 * 获取用户配置
 * @returns {Promise<Object>} - 返回用户配置对象
 */
async function getConfig() { ... }

/**
 * 缓存翻译结果
 * @param {string} key - 缓存键
 * @param {Object} data - 缓存数据
 * @returns {Promise<void>}
 */
async function cacheTranslation(key, data) { ... }

/**
 * 获取缓存的翻译结果
 * @param {string} key - 缓存键
 * @returns {Promise<Object|null>} - 返回缓存数据或null
 */
async function getCachedTranslation(key) { ... }
```

#### 实现思路
1. 使用Chrome Storage API存储用户配置
2. 实现缓存机制，避免重复翻译
3. 提供清除缓存和重置配置的功能

### 2.5 API模块 (api.js)

#### 功能描述
封装各种API调用，包括OpenAI API等。

#### 主要API
```javascript
/**
 * 调用OpenAI Vision API进行图像分析
 * @param {string} imageData - Base64编码的图像数据
 * @param {Object} options - API选项
 * @returns {Promise<Object>} - 返回API响应
 */
async function callVisionAPI(imageData, options = {}) { ... }

/**
 * 调用OpenAI Chat API进行文本翻译
 * @param {string} text - 需要翻译的文本
 * @param {string} targetLang - 目标语言
 * @param {Object} options - API选项
 * @returns {Promise<string>} - 返回翻译结果
 */
async function callChatAPI(text, targetLang, options = {}) { ... }
```

#### 实现思路
1. 封装API请求逻辑，处理认证和错误
2. 支持多种API提供商
3. 实现请求限流和重试机制

## 3. 用户界面设计

### 3.1 弹出窗口 (Popup)

#### 组件结构
- `PopupApp` - 主组件
- `ApiKeyInput` - API密钥输入组件
- `LanguageSelector` - 语言选择组件
- `TranslationToggle` - 翻译开关组件
- `ModeSelector` - 模式选择组件
- `StyleSlider` - 样式调整滑块组件

#### 状态管理
使用React的Context API管理全局状态，包括：
- 用户配置
- 插件状态（启用/禁用）
- 当前页面信息

### 3.2 选项页面 (Options)

#### 组件结构
- `OptionsApp` - 主组件
- `ApiSettings` - API设置组件
- `StyleSettings` - 样式设置组件
- `KeyboardShortcuts` - 快捷键设置组件
- `CacheManager` - 缓存管理组件
- `AdvancedSettings` - 高级设置组件

#### 功能点
- 详细的API配置（模型选择、温度等）
- 字体和样式自定义
- 快捷键配置
- 缓存管理
- 导入/导出配置

## 4. 工作流程详解

### 4.1 初始化流程

1. 用户安装插件后，首次打开会提示配置API密钥
2. 插件默认处于禁用状态，用户需手动启用
3. 后台脚本加载并初始化配置
4. 内容脚本注入到符合条件的页面中

### 4.2 翻译流程

1. 用户访问漫画页面并启用插件
2. 内容脚本检测页面中的图像元素
3. 对每个图像元素执行以下操作：
   a. 调用检测模块识别文字区域
   b. 提取文字内容
   c. 调用翻译模块翻译文字
   d. 调用渲染模块覆盖原文字
4. 用户可以通过弹出窗口调整翻译设置

### 4.3 用户交互流程

1. 用户可以通过点击插件图标打开弹出窗口
2. 在弹出窗口中，用户可以：
   a. 启用/禁用翻译功能
   b. 选择目标语言
   c. 切换翻译模式（实时/手动）
   d. 调整样式参数
3. 用户可以通过右键菜单访问快捷功能
4. 用户可以通过选项页面进行高级设置

## 5. 性能优化策略

### 5.1 图像处理优化

1. 使用Web Workers进行图像处理，避免阻塞主线程
2. 实现图像缩放处理，减少API请求数据量
3. 使用Canvas的离屏渲染技术提高绘制效率

### 5.2 API调用优化

1. 实现请求合并，减少API调用次数
2. 使用缓存机制，避免重复翻译
3. 实现请求队列和限流，避免超出API限制

### 5.3 渲染优化

1. 使用CSS硬件加速提高渲染性能
2. 实现增量渲染，优先处理可见区域
3. 使用requestAnimationFrame优化动画效果

## 6. 安全与隐私考虑

### 6.1 API密钥安全

1. 使用Chrome的安全存储API存储API密钥
2. 不将API密钥发送到除API提供商以外的任何服务器
3. 提供清除API密钥的选项

### 6.2 数据隐私

1. 所有数据处理尽可能在本地完成
2. 明确的隐私政策，说明数据使用方式
3. 不收集用户的浏览历史或个人信息

## 7. 测试计划

### 7.1 单元测试

1. 使用Jest测试各个模块的核心功能
2. 模拟API响应进行测试
3. 测试边界条件和错误处理

### 7.2 集成测试

1. 测试模块之间的交互
2. 测试完整的翻译流程
3. 测试不同配置下的行为

### 7.3 兼容性测试

1. 在不同版本的Chrome浏览器上测试
2. 在不同的漫画网站上测试
3. 测试不同语言之间的翻译效果

## 8. 部署与发布

### 8.1 构建流程

1. 使用Vite构建插件
2. 优化资源大小，减少插件体积
3. 生成生产环境的manifest文件

### 8.2 发布流程

1. 准备Chrome Web Store所需的材料（图标、截图、描述等）
2. 提交插件进行审核
3. 发布初始版本
4. 监控用户反馈并规划更新

## 9. 维护与更新计划

### 9.1 短期维护

1. 修复用户反馈的bug
2. 优化翻译质量和性能
3. 添加用户请求的小功能

### 9.2 长期更新

1. 支持更多漫画网站和格式
2. 添加更多翻译API选项
3. 改进样式匹配算法
4. 增加社区功能

## 10. 技术债务与风险

### 10.1 潜在技术债务

1. API依赖风险 - 过度依赖OpenAI API
2. 样式匹配算法复杂度 - 可能需要持续优化
3. 浏览器兼容性 - Chrome更新可能影响插件功能

### 10.2 缓解策略

1. 设计模块化架构，便于替换API提供商
2. 实现渐进式样式匹配，从基本功能开始
3. 密切关注Chrome API变更，及时更新插件
