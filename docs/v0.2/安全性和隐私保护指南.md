# 安全性和隐私保护指南

## 1. 安全概述

### 1.1 安全目标

确保漫画翻译插件在以下方面的安全性：
- **API密钥保护**: 安全存储和传输用户API密钥
- **数据隐私**: 保护用户翻译内容和配置信息
- **网络安全**: 防止中间人攻击和数据泄露
- **代码安全**: 防止恶意代码注入和执行
- **依赖安全**: 确保第三方依赖的安全性

### 1.2 安全原则

- **最小权限原则**: 只请求必要的权限
- **数据最小化**: 只收集和处理必要的数据
- **透明性原则**: 明确告知用户数据处理方式
- **用户控制**: 用户完全控制自己的数据
- **安全默认**: 默认启用安全配置

## 2. API密钥安全管理

### 2.1 密钥存储安全

```typescript
// src/utils/secure-storage.ts
export class SecureStorage {
  private static readonly ENCRYPTION_KEY = 'manga-translator-v1';
  
  /**
   * 加密API密钥
   */
  static async encryptApiKey(apiKey: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(apiKey);
    
    // 使用Web Crypto API进行加密
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(this.ENCRYPTION_KEY),
      { name: 'AES-GCM' },
      false,
      ['encrypt']
    );
    
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      data
    );
    
    // 将IV和加密数据组合
    const combined = new Uint8Array(iv.length + encrypted.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(encrypted), iv.length);
    
    return btoa(String.fromCharCode(...combined));
  }
  
  /**
   * 解密API密钥
   */
  static async decryptApiKey(encryptedKey: string): Promise<string> {
    try {
      const combined = new Uint8Array(
        atob(encryptedKey).split('').map(char => char.charCodeAt(0))
      );
      
      const iv = combined.slice(0, 12);
      const encrypted = combined.slice(12);
      
      const key = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(this.ENCRYPTION_KEY),
        { name: 'AES-GCM' },
        false,
        ['decrypt']
      );
      
      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        key,
        encrypted
      );
      
      return new TextDecoder().decode(decrypted);
    } catch (error) {
      console.error('API密钥解密失败:', error);
      throw new Error('API密钥解密失败');
    }
  }
}
```

### 2.2 密钥传输安全

```typescript
// src/api/secure-provider.ts
export class SecureProvider extends BaseProvider {
  private async secureRequest(url: string, options: RequestInit): Promise<Response> {
    const secureOptions: RequestInit = {
      ...options,
      headers: {
        ...options.headers,
        'Content-Security-Policy': "default-src 'self'",
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      }
    };
    
    // 验证URL安全性
    if (!this.isSecureUrl(url)) {
      throw new Error('不安全的API端点');
    }
    
    const response = await fetch(url, secureOptions);
    
    // 验证响应安全性
    if (!this.isSecureResponse(response)) {
      throw new Error('不安全的API响应');
    }
    
    return response;
  }
  
  private isSecureUrl(url: string): boolean {
    const secureDomains = [
      'api.openai.com',
      'api.deepseek.com',
      'api.anthropic.com',
      'dashscope.aliyuncs.com'
    ];
    
    try {
      const urlObj = new URL(url);
      return secureDomains.some(domain => urlObj.hostname === domain);
    } catch {
      return false;
    }
  }
  
  private isSecureResponse(response: Response): boolean {
    // 检查响应头安全性
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return false;
    }
    
    // 检查响应状态
    if (!response.ok) {
      return false;
    }
    
    return true;
  }
}
```

### 2.3 密钥轮换机制

```typescript
// src/utils/key-rotation.ts
export class KeyRotationManager {
  private static readonly KEY_EXPIRY_DAYS = 90; // 90天密钥有效期
  
  /**
   * 检查密钥是否需要轮换
   */
  static async checkKeyRotation(providerType: string): Promise<boolean> {
    const keyInfo = await this.getKeyInfo(providerType);
    
    if (!keyInfo) {
      return false;
    }
    
    const daysSinceCreation = (Date.now() - keyInfo.createdAt) / (1000 * 60 * 60 * 24);
    return daysSinceCreation > this.KEY_EXPIRY_DAYS;
  }
  
  /**
   * 提醒用户更新密钥
   */
  static async notifyKeyRotation(providerType: string): Promise<void> {
    const { toast } = useToast();
    
    toast({
      title: '安全提醒',
      description: `您的${providerType} API密钥已使用超过90天，建议更新以确保安全。`,
      variant: 'warning',
      action: (
        <ToastAction altText="更新密钥" onClick={() => this.openKeyUpdate(providerType)}>
          更新密钥
        </ToastAction>
      )
    });
  }
  
  private static async getKeyInfo(providerType: string): Promise<any> {
    const result = await chrome.storage.local.get(`key-info-${providerType}`);
    return result[`key-info-${providerType}`];
  }
  
  private static openKeyUpdate(providerType: string): void {
    chrome.runtime.openOptionsPage();
  }
}
```

## 3. 数据隐私保护

### 3.1 数据分类和处理

```typescript
// src/utils/privacy-manager.ts
export class PrivacyManager {
  // 数据分类定义
  static readonly DATA_CATEGORIES = {
    SENSITIVE: 'sensitive',      // API密钥、用户配置
    PERSONAL: 'personal',        // 翻译历史、使用偏好
    TECHNICAL: 'technical',      // 日志、性能数据
    PUBLIC: 'public'             // 公开配置、帮助信息
  } as const;
  
  /**
   * 数据脱敏处理
   */
  static sanitizeData(data: any, category: string): any {
    switch (category) {
      case this.DATA_CATEGORIES.SENSITIVE:
        return this.sanitizeSensitiveData(data);
      case this.DATA_CATEGORIES.PERSONAL:
        return this.sanitizePersonalData(data);
      case this.DATA_CATEGORIES.TECHNICAL:
        return this.sanitizeTechnicalData(data);
      default:
        return data;
    }
  }
  
  /**
   * 敏感数据脱敏
   */
  private static sanitizeSensitiveData(data: any): any {
    if (typeof data === 'object' && data !== null) {
      const sanitized = { ...data };
      
      // 脱敏API密钥
      if (sanitized.apiKey) {
        sanitized.apiKey = this.maskApiKey(sanitized.apiKey);
      }
      
      // 脱敏其他敏感字段
      const sensitiveFields = ['password', 'token', 'secret'];
      sensitiveFields.forEach(field => {
        if (sanitized[field]) {
          sanitized[field] = '***';
        }
      });
      
      return sanitized;
    }
    
    return data;
  }
  
  /**
   * API密钥脱敏显示
   */
  private static maskApiKey(apiKey: string): string {
    if (apiKey.length <= 8) {
      return '***';
    }
    
    return apiKey.substring(0, 4) + '***' + apiKey.substring(apiKey.length - 4);
  }
  
  /**
   * 个人数据脱敏
   */
  private static sanitizePersonalData(data: any): any {
    if (typeof data === 'object' && data !== null) {
      const sanitized = { ...data };
      
      // 脱敏翻译内容（保留前几个字符）
      if (sanitized.translatedText) {
        const text = sanitized.translatedText;
        if (text.length > 10) {
          sanitized.translatedText = text.substring(0, 10) + '...';
        }
      }
      
      return sanitized;
    }
    
    return data;
  }
}
```

### 3.2 数据生命周期管理

```typescript
// src/utils/data-lifecycle.ts
export class DataLifecycleManager {
  private static readonly DATA_RETENTION_DAYS = {
    translation_history: 30,    // 翻译历史保留30天
    error_logs: 7,              // 错误日志保留7天
    performance_data: 14,       // 性能数据保留14天
    debug_logs: 3               // 调试日志保留3天
  };
  
  /**
   * 定期清理过期数据
   */
  static async cleanupExpiredData(): Promise<void> {
    const now = Date.now();
    
    // 清理翻译历史
    await this.cleanupTranslationHistory(now);
    
    // 清理错误日志
    await this.cleanupErrorLogs(now);
    
    // 清理性能数据
    await this.cleanupPerformanceData(now);
    
    // 清理调试日志
    await this.cleanupDebugLogs(now);
  }
  
  /**
   * 清理翻译历史
   */
  private static async cleanupTranslationHistory(now: number): Promise<void> {
    const { history } = useTranslationStore.getState();
    const retentionMs = this.DATA_RETENTION_DAYS.translation_history * 24 * 60 * 60 * 1000;
    
    const filteredHistory = history.filter(item => 
      (now - item.timestamp) < retentionMs
    );
    
    if (filteredHistory.length !== history.length) {
      useTranslationStore.getState().setHistory(filteredHistory);
      console.log(`清理了${history.length - filteredHistory.length}条过期翻译历史`);
    }
  }
  
  /**
   * 用户数据导出
   */
  static async exportUserData(): Promise<string> {
    const data = {
      translation_history: useTranslationStore.getState().history,
      config: useConfigStore.getState(),
      export_timestamp: new Date().toISOString(),
      version: '1.0'
    };
    
    return JSON.stringify(data, null, 2);
  }
  
  /**
   * 用户数据删除
   */
  static async deleteUserData(): Promise<void> {
    // 清除所有存储的数据
    await chrome.storage.local.clear();
    await chrome.storage.sync.clear();
    
    // 重置状态
    useTranslationStore.getState().reset();
    useConfigStore.getState().reset();
    
    console.log('用户数据已完全删除');
  }
}
```

### 3.3 隐私设置管理

```typescript
// src/components/Options/PrivacySettings.tsx
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { usePrivacyStore } from '@/stores/privacy';

export function PrivacySettings() {
  const {
    analyticsEnabled,
    errorReportingEnabled,
    translationHistoryEnabled,
    setAnalyticsEnabled,
    setErrorReportingEnabled,
    setTranslationHistoryEnabled
  } = usePrivacyStore();

  const handleExportData = async () => {
    const data = await DataLifecycleManager.exportUserData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `manga-translator-data-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
  };

  const handleDeleteData = async () => {
    if (confirm('确定要删除所有数据吗？此操作不可撤销。')) {
      await DataLifecycleManager.deleteUserData();
      alert('数据已删除');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>隐私设置</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>翻译历史记录</Label>
            <p className="text-sm text-muted-foreground">
              保存翻译历史以便快速访问
            </p>
          </div>
          <Switch
            checked={translationHistoryEnabled}
            onCheckedChange={setTranslationHistoryEnabled}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>错误报告</Label>
            <p className="text-sm text-muted-foreground">
              自动发送错误报告以帮助改进
            </p>
          </div>
          <Switch
            checked={errorReportingEnabled}
            onCheckedChange={setErrorReportingEnabled}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>使用统计</Label>
            <p className="text-sm text-muted-foreground">
              收集匿名使用数据以改进功能
            </p>
          </div>
          <Switch
            checked={analyticsEnabled}
            onCheckedChange={setAnalyticsEnabled}
          />
        </div>

        <div className="flex gap-2 pt-4">
          <Button variant="outline" onClick={handleExportData}>
            导出数据
          </Button>
          <Button variant="destructive" onClick={handleDeleteData}>
            删除所有数据
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
```

## 4. 内容安全策略(CSP)

### 4.1 CSP配置

```json
// public/manifest.json
{
  "content_security_policy": {
    "extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self';",
    "sandbox": "sandbox allow-scripts allow-forms allow-popups allow-modals; script-src 'self' 'unsafe-inline' 'unsafe-eval'; child-src 'self'"
  }
}
```

### 4.2 动态CSP管理

```typescript
// src/utils/csp-manager.ts
export class CSPManager {
  private static readonly DEFAULT_CSP = {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'"],
    'style-src': ["'self'", "'unsafe-inline'"],
    'img-src': ["'self'", 'data:', 'https:'],
    'connect-src': [
      "'self'",
      'https://api.openai.com',
      'https://api.deepseek.com',
      'https://api.anthropic.com',
      'https://dashscope.aliyuncs.com'
    ],
    'frame-src': ["'none'"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"]
  };
  
  /**
   * 设置CSP头
   */
  static setCSPHeaders(): void {
    const cspString = this.buildCSPString(this.DEFAULT_CSP);
    
    // 在内容脚本中设置CSP
    const meta = document.createElement('meta');
    meta.httpEquiv = 'Content-Security-Policy';
    meta.content = cspString;
    document.head.appendChild(meta);
  }
  
  /**
   * 构建CSP字符串
   */
  private static buildCSPString(csp: Record<string, string[]>): string {
    return Object.entries(csp)
      .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
      .join('; ');
  }
  
  /**
   * 验证外部资源
   */
  static validateExternalResource(url: string): boolean {
    const allowedDomains = [
      'api.openai.com',
      'api.deepseek.com',
      'api.anthropic.com',
      'dashscope.aliyuncs.com'
    ];
    
    try {
      const urlObj = new URL(url);
      return allowedDomains.includes(urlObj.hostname);
    } catch {
      return false;
    }
  }
}
```

## 5. 第三方依赖安全

### 5.1 依赖安全审计

```json
// package.json
{
  "scripts": {
    "security:audit": "npm audit --audit-level=moderate",
    "security:fix": "npm audit fix",
    "security:check": "npm audit --json > security-report.json"
  },
  "devDependencies": {
    "audit-ci": "^6.0.0",
    "snyk": "^1.0.0"
  }
}
```

### 5.2 依赖监控配置

```yaml
# .github/workflows/security.yml
name: 安全审计

on:
  schedule:
    - cron: '0 0 * * 1'  # 每周一运行
  push:
    branches: [ main, develop ]

jobs:
  security-audit:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: 设置Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: 安装依赖
      run: npm ci
    
    - name: 运行安全审计
      run: npm run security:audit
    
    - name: 生成安全报告
      run: npm run security:check
    
    - name: 上传安全报告
      uses: actions/upload-artifact@v3
      with:
        name: security-report
        path: security-report.json
```

### 5.3 依赖更新策略

```typescript
// src/utils/dependency-manager.ts
export class DependencyManager {
  /**
   * 检查依赖更新
   */
  static async checkForUpdates(): Promise<DependencyUpdate[]> {
    try {
      const response = await fetch('https://registry.npmjs.org/-/v1/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: 'manga-translator',
          size: 1
        })
      });
      
      const data = await response.json();
      return this.parseUpdateInfo(data);
    } catch (error) {
      console.error('检查依赖更新失败:', error);
      return [];
    }
  }
  
  /**
   * 安全更新依赖
   */
  static async safeUpdateDependency(packageName: string, version: string): Promise<boolean> {
    try {
      // 备份当前版本
      await this.backupCurrentVersion(packageName);
      
      // 更新依赖
      const result = await this.updatePackage(packageName, version);
      
      // 运行测试确保更新安全
      const testsPassed = await this.runSecurityTests();
      
      if (!testsPassed) {
        await this.rollbackUpdate(packageName);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('依赖更新失败:', error);
      await this.rollbackUpdate(packageName);
      return false;
    }
  }
}
```

## 6. 网络安全防护

### 6.1 请求验证

```typescript
// src/utils/request-validator.ts
export class RequestValidator {
  /**
   * 验证API请求
   */
  static validateApiRequest(request: Request): boolean {
    // 验证请求方法
    if (!['GET', 'POST'].includes(request.method)) {
      return false;
    }
    
    // 验证Content-Type
    const contentType = request.headers.get('content-type');
    if (request.method === 'POST' && !contentType?.includes('application/json')) {
      return false;
    }
    
    // 验证请求大小
    const contentLength = request.headers.get('content-length');
    if (contentLength && parseInt(contentLength) > 10 * 1024 * 1024) { // 10MB限制
      return false;
    }
    
    return true;
  }
  
  /**
   * 验证响应数据
   */
  static validateApiResponse(response: Response, data: any): boolean {
    // 验证响应状态
    if (!response.ok) {
      return false;
    }
    
    // 验证响应格式
    if (!this.isValidResponseFormat(data)) {
      return false;
    }
    
    // 验证数据大小
    const dataSize = JSON.stringify(data).length;
    if (dataSize > 5 * 1024 * 1024) { // 5MB限制
      return false;
    }
    
    return true;
  }
  
  /**
   * 验证响应格式
   */
  private static isValidResponseFormat(data: any): boolean {
    if (!data || typeof data !== 'object') {
      return false;
    }
    
    // 检查必要字段
    const requiredFields = ['textAreas', 'translations'];
    return requiredFields.every(field => Array.isArray(data[field]));
  }
}
```

### 6.2 防重放攻击

```typescript
// src/utils/replay-protection.ts
export class ReplayProtection {
  private static readonly NONCE_EXPIRY = 5 * 60 * 1000; // 5分钟
  private static usedNonces = new Set<string>();
  
  /**
   * 生成防重放nonce
   */
  static generateNonce(): string {
    const nonce = crypto.getRandomValues(new Uint8Array(16));
    return Array.from(nonce, byte => byte.toString(16).padStart(2, '0')).join('');
  }
  
  /**
   * 验证nonce
   */
  static validateNonce(nonce: string, timestamp: number): boolean {
    // 检查nonce是否已使用
    if (this.usedNonces.has(nonce)) {
      return false;
    }
    
    // 检查时间戳是否过期
    const now = Date.now();
    if (now - timestamp > this.NONCE_EXPIRY) {
      return false;
    }
    
    // 标记nonce为已使用
    this.usedNonces.add(nonce);
    
    // 清理过期nonce
    setTimeout(() => {
      this.usedNonces.delete(nonce);
    }, this.NONCE_EXPIRY);
    
    return true;
  }
}
```

## 7. 安全监控和日志

### 7.1 安全事件监控

```typescript
// src/utils/security-monitor.ts
export class SecurityMonitor {
  private static securityEvents: SecurityEvent[] = [];
  
  /**
   * 记录安全事件
   */
  static logSecurityEvent(event: SecurityEvent): void {
    const enrichedEvent = {
      ...event,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      extensionVersion: chrome.runtime.getManifest().version
    };
    
    this.securityEvents.push(enrichedEvent);
    
    // 检查是否需要立即报告
    if (this.shouldReportImmediately(enrichedEvent)) {
      this.reportSecurityEvent(enrichedEvent);
    }
    
    // 限制日志大小
    if (this.securityEvents.length > 1000) {
      this.securityEvents = this.securityEvents.slice(-500);
    }
  }
  
  /**
   * 检测异常行为
   */
  static detectAnomalies(): SecurityAnomaly[] {
    const anomalies: SecurityAnomaly[] = [];
    
    // 检测频繁的API调用
    const recentApiCalls = this.securityEvents.filter(
      event => event.type === 'api_call' && 
      Date.now() - new Date(event.timestamp).getTime() < 60000
    );
    
    if (recentApiCalls.length > 10) {
      anomalies.push({
        type: 'rate_limit_exceeded',
        severity: 'warning',
        description: 'API调用频率异常',
        events: recentApiCalls
      });
    }
    
    // 检测失败的认证尝试
    const failedAuths = this.securityEvents.filter(
      event => event.type === 'auth_failure'
    );
    
    if (failedAuths.length > 5) {
      anomalies.push({
        type: 'multiple_auth_failures',
        severity: 'high',
        description: '多次认证失败',
        events: failedAuths
      });
    }
    
    return anomalies;
  }
  
  /**
   * 生成安全报告
   */
  static generateSecurityReport(): SecurityReport {
    const anomalies = this.detectAnomalies();
    const recentEvents = this.securityEvents.slice(-100);
    
    return {
      timestamp: new Date().toISOString(),
      totalEvents: this.securityEvents.length,
      recentEvents,
      anomalies,
      riskScore: this.calculateRiskScore(anomalies)
    };
  }
  
  private static calculateRiskScore(anomalies: SecurityAnomaly[]): number {
    let score = 0;
    
    anomalies.forEach(anomaly => {
      switch (anomaly.severity) {
        case 'low':
          score += 1;
          break;
        case 'warning':
          score += 3;
          break;
        case 'high':
          score += 10;
          break;
        case 'critical':
          score += 20;
          break;
      }
    });
    
    return Math.min(score, 100);
  }
}

// 类型定义
interface SecurityEvent {
  type: string;
  severity: 'low' | 'warning' | 'high' | 'critical';
  description: string;
  details?: any;
}

interface SecurityAnomaly {
  type: string;
  severity: 'low' | 'warning' | 'high' | 'critical';
  description: string;
  events: SecurityEvent[];
}

interface SecurityReport {
  timestamp: string;
  totalEvents: number;
  recentEvents: SecurityEvent[];
  anomalies: SecurityAnomaly[];
  riskScore: number;
}
```

## 8. 安全测试

### 8.1 安全测试用例

```typescript
// src/test/security.test.ts
import { describe, it, expect, vi } from 'vitest';
import { SecureStorage } from '../utils/secure-storage';
import { RequestValidator } from '../utils/request-validator';
import { CSPManager } from '../utils/csp-manager';

describe('安全测试', () => {
  it('API密钥应该正确加密', async () => {
    const testKey = 'sk-test123456789';
    const encrypted = await SecureStorage.encryptApiKey(testKey);
    const decrypted = await SecureStorage.decryptApiKey(encrypted);
    
    expect(decrypted).toBe(testKey);
    expect(encrypted).not.toBe(testKey);
  });
  
  it('应该拒绝不安全的URL', () => {
    const unsafeUrls = [
      'http://malicious.com/api',
      'https://fake-openai.com/v1',
      'javascript:alert("xss")'
    ];
    
    unsafeUrls.forEach(url => {
      expect(CSPManager.validateExternalResource(url)).toBe(false);
    });
  });
  
  it('应该验证API请求格式', () => {
    const validRequest = new Request('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify({ test: 'data' })
    });
    
    const invalidRequest = new Request('https://api.openai.com/v1/chat/completions', {
      method: 'PUT',
      headers: { 'content-type': 'text/plain' }
    });
    
    expect(RequestValidator.validateApiRequest(validRequest)).toBe(true);
    expect(RequestValidator.validateApiRequest(invalidRequest)).toBe(false);
  });
  
  it('应该检测XSS攻击', () => {
    const maliciousInputs = [
      '<script>alert("xss")</script>',
      'javascript:alert("xss")',
      'onerror=alert("xss")',
      '"><script>alert("xss")</script>'
    ];
    
    maliciousInputs.forEach(input => {
      expect(this.sanitizeInput(input)).not.toContain('<script>');
      expect(this.sanitizeInput(input)).not.toContain('javascript:');
    });
  });
  
  private sanitizeInput(input: string): string {
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }
});
```

## 9. 安全最佳实践

### 9.1 开发安全指南

1. **代码审查**
   - 所有代码变更必须经过安全审查
   - 重点关注用户输入处理和API调用
   - 检查第三方依赖的安全性

2. **输入验证**
   - 所有用户输入必须进行验证和清理
   - 使用白名单而不是黑名单
   - 实施适当的长度和格式限制

3. **错误处理**
   - 不要在生产环境中暴露敏感信息
   - 记录错误但不暴露系统细节
   - 实施优雅的错误恢复机制

4. **权限管理**
   - 只请求必要的Chrome权限
   - 定期审查权限使用情况
   - 提供权限说明和撤销选项

### 9.2 部署安全检查清单

- [ ] 所有API密钥已加密存储
- [ ] CSP策略已正确配置
- [ ] 第三方依赖已安全审计
- [ ] 错误处理不暴露敏感信息
- [ ] 用户数据隐私设置已实现
- [ ] 安全监控已启用
- [ ] 备份和恢复机制已测试
- [ ] 安全测试已通过

### 9.3 用户安全建议

1. **API密钥安全**
   - 定期轮换API密钥
   - 不要在公共场合分享密钥
   - 使用最小权限原则

2. **数据保护**
   - 定期导出和备份数据
   - 及时清理不需要的历史记录
   - 了解数据处理方式

3. **更新维护**
   - 及时更新插件版本
   - 关注安全公告
   - 报告安全问题

通过这个全面的安全性和隐私保护指南，可以确保漫画翻译插件在保护用户数据安全的同时，提供可靠和安全的服务。 