# 性能基准与监控方案

## 1. 性能基准定义

### 1.1 核心性能指标

#### 响应时间指标
- **翻译响应时间**: < 3秒 (目标: < 2秒)
- **UI响应时间**: < 100毫秒
- **插件启动时间**: < 500毫秒
- **配置加载时间**: < 200毫秒

#### 资源使用指标
- **内存使用**: < 50MB (目标: < 30MB)
- **CPU使用率**: < 20% (翻译期间)
- **网络请求大小**: < 2MB (单次翻译)
- **缓存命中率**: > 80%

#### 用户体验指标
- **首次翻译时间**: < 5秒
- **连续翻译间隔**: < 1秒
- **错误恢复时间**: < 2秒
- **界面渲染时间**: < 16毫秒 (60fps)

### 1.2 基准测试数据

```typescript
// src/test/benchmarks/performance-baseline.ts
export const PERFORMANCE_BASELINES = {
  translation: {
    simple_image: 1500,      // 简单图像翻译 (ms)
    complex_image: 2500,     // 复杂图像翻译 (ms)
    large_image: 3000,       // 大图像翻译 (ms)
    batch_translation: 5000  // 批量翻译 (ms)
  },
  ui: {
    popup_render: 50,        // 弹窗渲染 (ms)
    options_load: 100,       // 选项页加载 (ms)
    state_update: 10,        // 状态更新 (ms)
    component_mount: 30      // 组件挂载 (ms)
  },
  storage: {
    config_read: 20,         // 配置读取 (ms)
    config_write: 50,        // 配置写入 (ms)
    history_query: 30,       // 历史查询 (ms)
    cache_access: 5          // 缓存访问 (ms)
  },
  memory: {
    idle_usage: 15,          // 空闲内存使用 (MB)
    active_usage: 35,        // 活跃内存使用 (MB)
    peak_usage: 50,          // 峰值内存使用 (MB)
    cache_size: 10           // 缓存大小 (MB)
  }
};
```

## 2. 性能测试实现

### 2.1 翻译性能测试

```typescript
// src/test/benchmarks/translation-benchmark.test.ts
import { describe, it, expect } from 'vitest';
import { performance } from 'perf_hooks';
import { TranslationService } from '../../api/translation-service';
import { PERFORMANCE_BASELINES } from './performance-baseline';

describe('翻译性能基准测试', () => {
  let translationService: TranslationService;

  beforeEach(() => {
    translationService = new TranslationService();
  });

  it('简单图像翻译性能', async () => {
    const testImage = generateTestImage('simple');
    
    const startTime = performance.now();
    const result = await translationService.translateImage(testImage, 'zh-CN');
    const endTime = performance.now();
    
    const duration = endTime - startTime;
    
    expect(duration).toBeLessThan(PERFORMANCE_BASELINES.translation.simple_image);
    expect(result.translations).toBeDefined();
    
    console.log(`简单图像翻译耗时: ${duration.toFixed(2)}ms`);
  });

  it('复杂图像翻译性能', async () => {
    const testImage = generateTestImage('complex');
    
    const startTime = performance.now();
    const result = await translationService.translateImage(testImage, 'zh-CN');
    const endTime = performance.now();
    
    const duration = endTime - startTime;
    
    expect(duration).toBeLessThan(PERFORMANCE_BASELINES.translation.complex_image);
    expect(result.translations.length).toBeGreaterThan(0);
    
    console.log(`复杂图像翻译耗时: ${duration.toFixed(2)}ms`);
  });

  it('批量翻译性能', async () => {
    const testImages = [
      generateTestImage('simple'),
      generateTestImage('simple'),
      generateTestImage('simple')
    ];
    
    const startTime = performance.now();
    const results = await Promise.all(
      testImages.map(image => translationService.translateImage(image, 'zh-CN'))
    );
    const endTime = performance.now();
    
    const duration = endTime - startTime;
    
    expect(duration).toBeLessThan(PERFORMANCE_BASELINES.translation.batch_translation);
    expect(results).toHaveLength(3);
    
    console.log(`批量翻译耗时: ${duration.toFixed(2)}ms`);
  });
});
```

### 2.2 UI性能测试

```typescript
// src/test/benchmarks/ui-benchmark.test.ts
import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import { performance } from 'perf_hooks';
import { PopupApp } from '../../components/Popup/PopupApp';
import { PERFORMANCE_BASELINES } from './performance-baseline';

describe('UI性能基准测试', () => {
  it('弹窗渲染性能', () => {
    const startTime = performance.now();
    const { container } = render(<PopupApp />);
    const endTime = performance.now();
    
    const duration = endTime - startTime;
    
    expect(duration).toBeLessThan(PERFORMANCE_BASELINES.ui.popup_render);
    expect(container.firstChild).toBeTruthy();
    
    console.log(`弹窗渲染耗时: ${duration.toFixed(2)}ms`);
  });

  it('状态更新性能', () => {
    const { rerender } = render(<PopupApp />);
    
    const startTime = performance.now();
    
    // 触发状态更新
    useTranslationStore.getState().setEnabled(true);
    rerender(<PopupApp />);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    expect(duration).toBeLessThan(PERFORMANCE_BASELINES.ui.state_update);
    
    console.log(`状态更新耗时: ${duration.toFixed(2)}ms`);
  });
});
```

### 2.3 内存使用测试

```typescript
// src/test/benchmarks/memory-benchmark.test.ts
describe('内存使用基准测试', () => {
  it('空闲状态内存使用', async () => {
    const initialMemory = getMemoryUsage();
    
    // 等待垃圾回收
    await forceGarbageCollection();
    
    const idleMemory = getMemoryUsage();
    const memoryUsage = idleMemory - initialMemory;
    
    expect(memoryUsage).toBeLessThan(PERFORMANCE_BASELINES.memory.idle_usage * 1024 * 1024);
    
    console.log(`空闲内存使用: ${(memoryUsage / 1024 / 1024).toFixed(2)}MB`);
  });

  it('翻译过程内存使用', async () => {
    const initialMemory = getMemoryUsage();
    
    // 执行翻译
    await translationService.translateImage(generateTestImage('large'), 'zh-CN');
    
    const peakMemory = getMemoryUsage();
    const memoryUsage = peakMemory - initialMemory;
    
    expect(memoryUsage).toBeLessThan(PERFORMANCE_BASELINES.memory.peak_usage * 1024 * 1024);
    
    console.log(`翻译峰值内存使用: ${(memoryUsage / 1024 / 1024).toFixed(2)}MB`);
  });

  it('缓存内存使用', async () => {
    const cache = new TranslationCache();
    
    // 填充缓存
    for (let i = 0; i < 50; i++) {
      await cache.set(`key-${i}`, generateMockTranslationResult());
    }
    
    const cacheMemory = await cache.getMemoryUsage();
    
    expect(cacheMemory).toBeLessThan(PERFORMANCE_BASELINES.memory.cache_size * 1024 * 1024);
    
    console.log(`缓存内存使用: ${(cacheMemory / 1024 / 1024).toFixed(2)}MB`);
  });
});
```

## 3. 实时性能监控

### 3.1 性能监控器

```typescript
// src/utils/performance-monitor.ts
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  private observers: PerformanceObserver[] = [];

  static getInstance(): PerformanceMonitor {
    if (!this.instance) {
      this.instance = new PerformanceMonitor();
    }
    return this.instance;
  }

  /**
   * 开始性能监控
   */
  startMonitoring(): void {
    this.monitorTranslationPerformance();
    this.monitorUIPerformance();
    this.monitorMemoryUsage();
    this.monitorNetworkPerformance();
  }

  /**
   * 记录翻译性能
   */
  recordTranslationTime(duration: number): void {
    this.recordMetric('translation_time', duration);
    
    if (duration > PERFORMANCE_BASELINES.translation.simple_image) {
      console.warn(`翻译耗时超过基准: ${duration}ms`);
    }
  }

  /**
   * 记录UI性能
   */
  recordUIRenderTime(componentName: string, duration: number): void {
    this.recordMetric(`ui_render_${componentName}`, duration);
    
    if (duration > 16) { // 60fps基准
      console.warn(`${componentName}渲染耗时过长: ${duration}ms`);
    }
  }

  /**
   * 记录内存使用
   */
  recordMemoryUsage(usage: number): void {
    this.recordMetric('memory_usage', usage);
    
    if (usage > PERFORMANCE_BASELINES.memory.peak_usage * 1024 * 1024) {
      console.warn(`内存使用超过基准: ${(usage / 1024 / 1024).toFixed(2)}MB`);
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [metric, values] of this.metrics) {
      stats[metric] = {
        count: values.length,
        average: values.reduce((a, b) => a + b, 0) / values.length,
        min: Math.min(...values),
        max: Math.max(...values),
        p95: this.calculatePercentile(values, 95),
        p99: this.calculatePercentile(values, 99)
      };
    }
    
    return stats;
  }

  /**
   * 监控翻译性能
   */
  private monitorTranslationPerformance(): void {
    // 使用Performance API监控
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name.startsWith('translation-')) {
          this.recordTranslationTime(entry.duration);
        }
      }
    });
    
    observer.observe({ entryTypes: ['measure'] });
    this.observers.push(observer);
  }

  /**
   * 监控UI性能
   */
  private monitorUIPerformance(): void {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'paint') {
          this.recordMetric(`paint_${entry.name}`, entry.startTime);
        }
      }
    });
    
    observer.observe({ entryTypes: ['paint', 'navigation'] });
    this.observers.push(observer);
  }

  /**
   * 监控内存使用
   */
  private monitorMemoryUsage(): void {
    setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        this.recordMemoryUsage(memory.usedJSHeapSize);
      }
    }, 5000); // 每5秒检查一次
  }

  /**
   * 监控网络性能
   */
  private monitorNetworkPerformance(): void {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource' && entry.name.includes('api')) {
          this.recordMetric('api_request_time', entry.duration);
        }
      }
    });
    
    observer.observe({ entryTypes: ['resource'] });
    this.observers.push(observer);
  }

  private recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // 保持最近1000个数据点
    if (values.length > 1000) {
      values.shift();
    }
  }

  private calculatePercentile(values: number[], percentile: number): number {
    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index];
  }
}
```

### 3.2 性能装饰器

```typescript
// src/utils/performance-decorators.ts
export function measurePerformance(name: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const startTime = performance.now();
      performance.mark(`${name}-start`);
      
      try {
        const result = await originalMethod.apply(this, args);
        
        const endTime = performance.now();
        performance.mark(`${name}-end`);
        performance.measure(name, `${name}-start`, `${name}-end`);
        
        const duration = endTime - startTime;
        PerformanceMonitor.getInstance().recordTranslationTime(duration);
        
        return result;
      } catch (error) {
        performance.mark(`${name}-error`);
        throw error;
      }
    };
    
    return descriptor;
  };
}

// 使用示例
export class TranslationService {
  @measurePerformance('translation-image')
  async translateImage(imageData: string, targetLanguage: string): Promise<TranslationResult> {
    // 翻译逻辑
  }
}
```

### 3.3 性能报告生成

```typescript
// src/utils/performance-reporter.ts
export class PerformanceReporter {
  /**
   * 生成性能报告
   */
  static generateReport(): PerformanceReport {
    const monitor = PerformanceMonitor.getInstance();
    const stats = monitor.getPerformanceStats();
    
    return {
      timestamp: new Date().toISOString(),
      summary: this.generateSummary(stats),
      details: stats,
      recommendations: this.generateRecommendations(stats),
      baseline_comparison: this.compareWithBaseline(stats)
    };
  }

  /**
   * 生成性能摘要
   */
  private static generateSummary(stats: Record<string, any>): PerformanceSummary {
    const translationStats = stats.translation_time || {};
    const memoryStats = stats.memory_usage || {};
    
    return {
      average_translation_time: translationStats.average || 0,
      p95_translation_time: translationStats.p95 || 0,
      average_memory_usage: memoryStats.average || 0,
      peak_memory_usage: memoryStats.max || 0,
      total_translations: translationStats.count || 0,
      performance_score: this.calculatePerformanceScore(stats)
    };
  }

  /**
   * 生成性能建议
   */
  private static generateRecommendations(stats: Record<string, any>): string[] {
    const recommendations: string[] = [];
    
    const translationStats = stats.translation_time || {};
    if (translationStats.average > PERFORMANCE_BASELINES.translation.simple_image) {
      recommendations.push('翻译响应时间超过基准，建议优化API调用或增加缓存');
    }
    
    const memoryStats = stats.memory_usage || {};
    if (memoryStats.max > PERFORMANCE_BASELINES.memory.peak_usage * 1024 * 1024) {
      recommendations.push('内存使用超过基准，建议检查内存泄漏或优化缓存策略');
    }
    
    const uiStats = Object.keys(stats).filter(key => key.startsWith('ui_render_'));
    const slowUIComponents = uiStats.filter(key => stats[key].average > 16);
    if (slowUIComponents.length > 0) {
      recommendations.push(`以下UI组件渲染较慢: ${slowUIComponents.join(', ')}`);
    }
    
    return recommendations;
  }

  /**
   * 与基准对比
   */
  private static compareWithBaseline(stats: Record<string, any>): BaselineComparison {
    const comparison: BaselineComparison = {};
    
    const translationStats = stats.translation_time || {};
    comparison.translation_performance = {
      current: translationStats.average || 0,
      baseline: PERFORMANCE_BASELINES.translation.simple_image,
      ratio: (translationStats.average || 0) / PERFORMANCE_BASELINES.translation.simple_image,
      status: (translationStats.average || 0) <= PERFORMANCE_BASELINES.translation.simple_image ? 'good' : 'poor'
    };
    
    const memoryStats = stats.memory_usage || {};
    comparison.memory_performance = {
      current: memoryStats.average || 0,
      baseline: PERFORMANCE_BASELINES.memory.active_usage * 1024 * 1024,
      ratio: (memoryStats.average || 0) / (PERFORMANCE_BASELINES.memory.active_usage * 1024 * 1024),
      status: (memoryStats.average || 0) <= PERFORMANCE_BASELINES.memory.active_usage * 1024 * 1024 ? 'good' : 'poor'
    };
    
    return comparison;
  }

  /**
   * 计算性能评分
   */
  private static calculatePerformanceScore(stats: Record<string, any>): number {
    let score = 100;
    
    // 翻译性能评分 (40%)
    const translationStats = stats.translation_time || {};
    if (translationStats.average > PERFORMANCE_BASELINES.translation.simple_image) {
      score -= 40 * (translationStats.average / PERFORMANCE_BASELINES.translation.simple_image - 1);
    }
    
    // 内存使用评分 (30%)
    const memoryStats = stats.memory_usage || {};
    if (memoryStats.max > PERFORMANCE_BASELINES.memory.peak_usage * 1024 * 1024) {
      score -= 30 * (memoryStats.max / (PERFORMANCE_BASELINES.memory.peak_usage * 1024 * 1024) - 1);
    }
    
    // UI性能评分 (30%)
    const uiStats = Object.keys(stats).filter(key => key.startsWith('ui_render_'));
    const avgUITime = uiStats.reduce((sum, key) => sum + (stats[key].average || 0), 0) / uiStats.length;
    if (avgUITime > 16) {
      score -= 30 * (avgUITime / 16 - 1);
    }
    
    return Math.max(0, Math.min(100, score));
  }
}

// 类型定义
interface PerformanceReport {
  timestamp: string;
  summary: PerformanceSummary;
  details: Record<string, any>;
  recommendations: string[];
  baseline_comparison: BaselineComparison;
}

interface PerformanceSummary {
  average_translation_time: number;
  p95_translation_time: number;
  average_memory_usage: number;
  peak_memory_usage: number;
  total_translations: number;
  performance_score: number;
}

interface BaselineComparison {
  translation_performance?: {
    current: number;
    baseline: number;
    ratio: number;
    status: 'good' | 'poor';
  };
  memory_performance?: {
    current: number;
    baseline: number;
    ratio: number;
    status: 'good' | 'poor';
  };
}
```

## 4. 性能优化建议

### 4.1 翻译性能优化

1. **智能缓存策略**
   - 实现基于图像哈希的缓存
   - 使用LRU算法管理缓存
   - 预加载常用翻译结果

2. **API调用优化**
   - 实现请求去重
   - 使用连接池
   - 实现智能重试机制

3. **图像处理优化**
   - 图像压缩和格式优化
   - 分块处理大图像
   - 使用Web Worker处理图像

### 4.2 UI性能优化

1. **React优化**
   - 使用React.memo和useMemo
   - 实现虚拟滚动
   - 优化组件渲染

2. **CSS优化**
   - 使用CSS-in-JS优化
   - 减少重排和重绘
   - 使用GPU加速

### 4.3 内存优化

1. **内存管理**
   - 及时清理事件监听器
   - 实现对象池
   - 优化缓存大小

2. **垃圾回收优化**
   - 避免内存泄漏
   - 合理使用WeakMap和WeakSet
   - 定期清理无用数据

通过这个全面的性能基准与监控方案，可以确保v0.2重构后的性能达到预期目标，并持续监控和优化性能表现。
