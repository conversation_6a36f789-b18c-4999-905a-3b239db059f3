# 技术债务清理指南

## 1. 当前技术债务分析

### 1.1 代码结构问题

- **文件组织混乱**: 组件、工具函数、API 调用分散在不同目录
- **命名不一致**: 变量、函数、组件命名风格不统一
- **类型定义缺失**: 大量 any 类型，缺乏 TypeScript 类型安全
- **重复代码**: 相似功能在不同文件中重复实现

### 1.2 状态管理问题

- **状态分散**: 使用多个 useState，缺乏统一管理
- **数据流混乱**: 组件间数据传递复杂，容易出错
- **持久化不一致**: 部分状态使用 Chrome Storage，部分使用 localStorage

### 1.3 性能问题

- **不必要的重渲染**: 缺乏 React.memo 和 useMemo 优化
- **API 调用效率低**: 没有缓存机制，重复请求
- **内存泄漏**: 事件监听器未正确清理

### 1.4 错误处理问题

- **错误边界缺失**: 没有全局错误处理机制
- **用户反馈不足**: 错误信息不够友好
- **调试困难**: 缺乏错误日志和调试工具

## 2. 清理计划

### 2.1 第一阶段：代码结构整理（1 周）

#### 2.1.1 文件重组织

```bash
# 创建新的目录结构
mkdir -p src/{components,stores,hooks,utils,types,constants,api}
mkdir -p src/components/{ui,Popup,Options,Content}
mkdir -p src/stores
mkdir -p src/hooks
mkdir -p src/utils
mkdir -p src/types
mkdir -p src/constants
mkdir -p src/api/{providers,ocr}
```

#### 2.1.2 文件迁移

```bash
# 迁移现有文件到新结构
mv src/components/Popup/* src/components/Popup/
mv src/components/Options/* src/components/Options/
mv src/api/providers/* src/api/providers/
mv src/api/ocr/* src/api/ocr/
```

#### 2.1.3 类型定义整理

```typescript
// src/types/index.ts
export interface TranslationState {
  enabled: boolean;
  mode: "manual" | "auto";
  targetLanguage: string;
  processing: boolean;
  currentImage: string | null;
  history: TranslationHistory[];
}

export interface TranslationHistory {
  id: string;
  imageUrl: string;
  translatedText: string;
  timestamp: number;
  targetLanguage: string;
}

export interface ConfigState {
  providerType: string;
  providerConfig: Record<string, ProviderConfig>;
  styleLevel: number;
  shortcuts: Shortcuts;
  advancedSettings: AdvancedSettings;
}

export interface ProviderConfig {
  apiKey: string;
  apiBaseUrl: string;
  visionModel: string;
  chatModel: string;
  temperature: number;
  maxTokens: number;
}

export interface Shortcuts {
  toggleTranslation: string;
  translateSelected: string;
}

export interface AdvancedSettings {
  debugMode: boolean;
  showOriginalText: boolean;
  renderType: "overlay" | "replace";
  useCorsProxy: boolean;
}

export interface TextArea {
  x: number;
  y: number;
  width: number;
  height: number;
  text: string;
  type: "bubble" | "narration" | "sfx" | "other";
  order: number;
}

export interface TranslationResult {
  textAreas: TextArea[];
  translations: string[];
  imageData: string;
  targetLanguage: string;
  timestamp: number;
}
```

### 2.2 第二阶段：状态管理重构（1 周）

#### 2.2.1 移除分散状态

```typescript
// 清理前：分散的状态管理
const [enabled, setEnabled] = useState(false);
const [mode, setMode] = useState("manual");
const [targetLanguage, setTargetLanguage] = useState("zh-CN");
const [processing, setProcessing] = useState(false);

// 清理后：统一的状态管理
const {
  enabled,
  mode,
  targetLanguage,
  processing,
  setEnabled,
  setMode,
  setTargetLanguage,
  setProcessing,
} = useTranslationStore();
```

#### 2.2.2 数据流优化

```typescript
// 清理前：复杂的数据传递
const handleTranslation = async (image) => {
  const config = await getConfig();
  const result = await translateImage(
    image,
    config.targetLanguage,
    config.providerType
  );
  // 处理结果...
};

// 清理后：简化的数据流
const { mutate: translateImage, isLoading } = useTranslateMutation();
const { targetLanguage } = useTranslationStore();

const handleTranslation = (image) => {
  translateImage({ imageData: image.src, imageUrl: image.src });
};
```

### 2.3 第三阶段：性能优化（1 周）

#### 2.3.1 组件优化

```typescript
// 清理前：不必要的重渲染
const PopupApp = () => {
  const [config, setConfig] = useState({});

  useEffect(() => {
    chrome.storage.sync.get(null, setConfig);
  }, []);

  return (
    <div>
      <ApiKeyInput value={config.apiKey} onChange={setConfig} />
      <LanguageSelector value={config.targetLanguage} onChange={setConfig} />
    </div>
  );
};

// 清理后：优化的组件
const PopupApp = React.memo(() => {
  const {
    providerType,
    providerConfig,
    setProviderType,
    updateProviderConfig,
  } = useConfigStore();
  const {
    enabled,
    mode,
    targetLanguage,
    setEnabled,
    setMode,
    setTargetLanguage,
  } = useTranslationStore();

  const currentConfig = useMemo(
    () => providerConfig[providerType],
    [providerType, providerConfig]
  );

  return (
    <div>
      <ApiKeyInput
        value={currentConfig?.apiKey}
        onChange={(apiKey) => updateProviderConfig(providerType, { apiKey })}
      />
      <LanguageSelector value={targetLanguage} onChange={setTargetLanguage} />
    </div>
  );
});
```

#### 2.3.2 缓存优化

```typescript
// 清理前：无缓存机制
const translateImage = async (imageData, targetLanguage) => {
  const response = await fetch("/api/translate", {
    method: "POST",
    body: JSON.stringify({ imageData, targetLanguage }),
  });
  return response.json();
};

// 清理后：智能缓存
const useTranslation = (imageData: string | null) => {
  const { targetLanguage } = useTranslationStore();
  const { providerType, providerConfig } = useConfigStore();

  return useQuery({
    queryKey: ["translation", imageData, targetLanguage, providerType],
    queryFn: () =>
      translateImage(
        imageData!,
        targetLanguage,
        providerType,
        providerConfig[providerType]
      ),
    enabled: !!imageData,
    staleTime: 10 * 60 * 1000, // 10分钟缓存
    cacheTime: 30 * 60 * 1000, // 30分钟缓存
  });
};
```

### 2.4 第四阶段：错误处理完善（1 周）

#### 2.4.1 错误边界实现

```typescript
// 清理前：无错误处理
const translateImage = async (image) => {
  const result = await api.translate(image);
  return result;
};

// 清理后：完善的错误处理
const translateImage = async (image) => {
  try {
    const result = await api.translate(image);
    return result;
  } catch (error) {
    if (error.name === "APIError") {
      throw new TranslationError("翻译服务暂时不可用，请稍后重试", error);
    } else if (error.name === "NetworkError") {
      throw new TranslationError("网络连接失败，请检查网络设置", error);
    } else {
      throw new TranslationError("翻译过程中出现未知错误", error);
    }
  }
};
```

#### 2.4.2 用户友好的错误提示

```typescript
// 清理前：控制台错误
console.error("Translation failed:", error);

// 清理后：用户友好的错误提示
const { toast } = useToast();
const { handleError } = useErrorHandler();

const handleTranslationError = (error) => {
  handleError(error, "translation");

  toast({
    title: "翻译失败",
    description: error.userMessage || "请稍后重试",
    variant: "destructive",
    action: (
      <ToastAction altText="重试" onClick={retryTranslation}>
        重试
      </ToastAction>
    ),
  });
};
```

## 3. 具体清理任务

### 3.1 代码重复清理

#### 3.1.1 API 调用重复

```typescript
// 清理前：重复的API调用逻辑
// 在多个文件中都有类似的代码
const callOpenAI = async (data) => {
  const response = await fetch("https://api.openai.com/v1/chat/completions", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${apiKey}`,
    },
    body: JSON.stringify(data),
  });
  return response.json();
};

// 清理后：统一的API调用
// src/api/base-provider.ts
export class AIProvider {
  protected async makeRequest(url: string, options: RequestInit) {
    const response = await fetch(url, {
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new APIError(`API请求失败: ${response.status}`, response.status);
    }

    return response.json();
  }
}
```

#### 3.1.2 配置管理重复

```typescript
// 清理前：分散的配置管理
// 在多个组件中都有类似的配置读取逻辑
useEffect(() => {
  chrome.storage.sync.get(["apiKey", "targetLanguage"], (result) => {
    setApiKey(result.apiKey || "");
    setTargetLanguage(result.targetLanguage || "zh-CN");
  });
}, []);

// 清理后：统一的状态管理
// 使用Zustand统一管理所有配置
const { apiKey, targetLanguage, setApiKey, setTargetLanguage } =
  useConfigStore();
```

### 3.2 类型安全改进

#### 3.2.1 移除 any 类型

```typescript
// 清理前：大量any类型
const handleConfig = (config: any) => {
  if (config.apiKey) {
    setApiKey(config.apiKey);
  }
  if (config.targetLanguage) {
    setTargetLanguage(config.targetLanguage);
  }
};

// 清理后：严格的类型定义
interface Config {
  apiKey?: string;
  targetLanguage?: string;
  providerType?: string;
  styleLevel?: number;
}

const handleConfig = (config: Config) => {
  if (config.apiKey) {
    setApiKey(config.apiKey);
  }
  if (config.targetLanguage) {
    setTargetLanguage(config.targetLanguage);
  }
};
```

#### 3.2.2 函数参数类型化

```typescript
// 清理前：无类型参数
const translateText = (text, targetLang, options) => {
  // 实现...
};

// 清理后：完整类型定义
const translateText = (
  text: string | string[],
  targetLang: SupportedLanguage,
  options: TranslationOptions = {}
): Promise<string | string[]> => {
  // 实现...
};

type SupportedLanguage = "zh-CN" | "zh-TW" | "en" | "ja" | "ko";
interface TranslationOptions {
  preserveFormat?: boolean;
  context?: string;
  style?: "formal" | "casual" | "manga";
}
```

### 3.3 性能优化

#### 3.3.1 事件监听器清理

```typescript
// 清理前：可能的内存泄漏
useEffect(() => {
  const handleClick = (e) => {
    if (e.target.tagName === "IMG") {
      handleImageClick(e);
    }
  };

  document.addEventListener("click", handleClick);
  // 缺少清理逻辑
}, []);

// 清理后：正确的清理
useEffect(() => {
  const handleClick = (e) => {
    if (e.target.tagName === "IMG") {
      handleImageClick(e);
    }
  };

  document.addEventListener("click", handleClick);

  return () => {
    document.removeEventListener("click", handleClick);
  };
}, []);
```

#### 3.3.2 防抖优化

```typescript
// 清理前：频繁的API调用
const handleScroll = () => {
  if (isNearBottom()) {
    loadMoreImages();
  }
};

// 清理后：防抖优化
const debouncedHandleScroll = useMemo(
  () =>
    debounce(() => {
      if (isNearBottom()) {
        loadMoreImages();
      }
    }, 100),
  []
);

useEffect(() => {
  window.addEventListener("scroll", debouncedHandleScroll);
  return () => window.removeEventListener("scroll", debouncedHandleScroll);
}, [debouncedHandleScroll]);
```

### 3.4 代码质量提升

#### 3.4.1 常量提取

```typescript
// 清理前：魔法数字和字符串
const API_BASE_URL = "https://api.openai.com/v1";
const MAX_RETRIES = 3;
const CACHE_TIME = 10 * 60 * 1000;

// 清理后：集中管理常量
// src/constants/index.ts
export const API_CONFIG = {
  OPENAI_BASE_URL: "https://api.openai.com/v1",
  DEEPSEEK_BASE_URL: "https://api.deepseek.com/v1",
  CLAUDE_BASE_URL: "https://api.anthropic.com/v1",
} as const;

export const CACHE_CONFIG = {
  STALE_TIME: 10 * 60 * 1000, // 10分钟
  CACHE_TIME: 30 * 60 * 1000, // 30分钟
  MAX_SIZE: 100,
} as const;

export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  BASE_DELAY: 1000,
  MAX_DELAY: 30000,
} as const;
```

#### 3.4.2 工具函数优化

```typescript
// 清理前：重复的工具函数
const formatError = (error) => {
  if (error.message.includes("API key")) {
    return "API密钥无效";
  }
  return error.message;
};

// 清理后：统一的工具函数
// src/utils/error.ts
export class TranslationError extends Error {
  constructor(
    message: string,
    public originalError?: Error,
    public userMessage?: string
  ) {
    super(message);
    this.name = "TranslationError";
  }
}

export const formatError = (error: Error): string => {
  if (error.message.includes("API key")) {
    return "API密钥无效或已过期，请在设置中更新";
  }
  if (error.message.includes("rate limit")) {
    return "API请求频率超限，请稍后再试";
  }
  if (error.message.includes("network")) {
    return "网络连接失败，请检查网络设置";
  }
  return error.message || "未知错误";
};
```

## 4. 迁移检查清单

### 4.1 代码结构检查

- [ ] 所有文件移动到正确的目录
- [ ] 导入路径更新
- [ ] 文件命名符合规范
- [ ] 目录结构清晰合理

### 4.2 类型安全检查

- [ ] 移除所有 any 类型
- [ ] 为所有函数添加类型定义
- [ ] 为所有接口添加类型定义
- [ ] 启用 TypeScript 严格模式

### 4.3 状态管理检查

- [ ] 所有状态迁移到 Zustand
- [ ] 移除所有 useState 和 useReducer
- [ ] 数据流简化
- [ ] 持久化配置正确

### 4.4 性能优化检查

- [ ] 添加 React.memo 优化
- [ ] 使用 useMemo 和 useCallback
- [ ] 事件监听器正确清理
- [ ] 实现缓存机制

### 4.5 错误处理检查

- [ ] 添加错误边界
- [ ] 统一错误处理
- [ ] 用户友好的错误提示
- [ ] 错误日志记录

### 4.6 代码质量检查

- [ ] ESLint 检查通过
- [ ] Prettier 格式化
- [ ] 单元测试通过
- [ ] 代码覆盖率达标

## 5. 验证和测试

### 5.1 功能测试

```bash
# 运行所有测试
npm test

# 运行特定测试
npm test -- --grep "translation"

# 检查测试覆盖率
npm run test:coverage
```

### 5.2 性能测试

```bash
# 构建性能分析
npm run build -- --profile

# 运行性能测试
npm run test:performance
```

### 5.3 兼容性测试

```bash
# 在不同Chrome版本测试
npm run test:compatibility

# 在不同网站测试
npm run test:websites
```

## 6. 部署和监控

### 6.1 部署检查

- [ ] 构建成功
- [ ] 插件加载正常
- [ ] 功能测试通过
- [ ] 性能指标达标

### 6.2 监控设置

- [ ] 错误监控配置
- [ ] 性能监控配置
- [ ] 用户行为分析
- [ ] 使用统计收集

通过这个技术债务清理指南，可以系统性地改进代码质量，提升开发效率和用户体验。每个阶段都有明确的目标和检查清单，确保重构过程的顺利进行。
