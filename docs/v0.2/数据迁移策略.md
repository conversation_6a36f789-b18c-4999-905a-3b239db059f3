# 数据迁移策略

## 1. 迁移概述

### 1.1 迁移目标

从v0.1的分散状态管理迁移到v0.2的Zustand统一状态管理，确保：
- 现有用户配置完整保留
- 翻译历史数据不丢失
- 用户无感知的平滑升级
- 向后兼容性支持

### 1.2 数据结构对比

#### v0.1 数据结构
```javascript
// Chrome Storage中的数据格式
{
  "apiKey": "sk-xxx",
  "providerType": "openai",
  "targetLanguage": "zh-CN",
  "enabled": true,
  "mode": "manual",
  "styleLevel": 50,
  "translationHistory": [
    {
      "imageUrl": "...",
      "translatedText": "...",
      "timestamp": **********
    }
  ]
}
```

#### v0.2 数据结构
```javascript
// Zustand持久化存储格式
{
  "manga-translator-storage": {
    "enabled": true,
    "mode": "manual",
    "targetLanguage": "zh-CN",
    "history": [
      {
        "id": "uuid",
        "imageUrl": "...",
        "translatedText": "...",
        "timestamp": **********,
        "targetLanguage": "zh-CN"
      }
    ]
  },
  "manga-translator-config": {
    "providerType": "openai",
    "providerConfig": {
      "openai": {
        "apiKey": "sk-xxx",
        "apiBaseUrl": "https://api.openai.com/v1",
        "visionModel": "gpt-4-vision-preview",
        "chatModel": "gpt-3.5-turbo",
        "temperature": 0.3,
        "maxTokens": 1000
      }
    },
    "styleLevel": 50,
    "shortcuts": {
      "toggleTranslation": "Ctrl+Shift+T",
      "translateSelected": "Ctrl+Shift+S"
    },
    "advancedSettings": {
      "debugMode": false,
      "showOriginalText": false,
      "renderType": "overlay",
      "useCorsProxy": false
    }
  }
}
```

## 2. 迁移实施方案

### 2.1 迁移时机

在应用初始化时检查是否需要迁移：

```typescript
// src/utils/migration.ts
export class DataMigration {
  private static readonly MIGRATION_VERSION_KEY = 'migration-version';
  private static readonly CURRENT_VERSION = '0.2.0';

  static async checkAndMigrate(): Promise<void> {
    const migrationVersion = await this.getMigrationVersion();
    
    if (!migrationVersion || migrationVersion < '0.2.0') {
      await this.migrateFromV01();
      await this.setMigrationVersion(this.CURRENT_VERSION);
    }
  }

  private static async getMigrationVersion(): Promise<string | null> {
    const result = await chrome.storage.local.get(this.MIGRATION_VERSION_KEY);
    return result[this.MIGRATION_VERSION_KEY] || null;
  }

  private static async setMigrationVersion(version: string): Promise<void> {
    await chrome.storage.local.set({ [this.MIGRATION_VERSION_KEY]: version });
  }
}
```

### 2.2 v0.1到v0.2迁移逻辑

```typescript
private static async migrateFromV01(): Promise<void> {
  try {
    // 读取v0.1格式的数据
    const v01Data = await chrome.storage.sync.get(null);
    
    if (Object.keys(v01Data).length === 0) {
      // 新用户，无需迁移
      return;
    }

    // 迁移翻译状态
    const translationState = {
      enabled: v01Data.enabled ?? false,
      mode: v01Data.mode ?? 'manual',
      targetLanguage: v01Data.targetLanguage ?? 'zh-CN',
      history: this.migrateHistory(v01Data.translationHistory || [])
    };

    // 迁移配置状态
    const configState = {
      providerType: v01Data.providerType ?? 'openai',
      providerConfig: this.migrateProviderConfig(v01Data),
      styleLevel: v01Data.styleLevel ?? 50,
      shortcuts: {
        toggleTranslation: 'Ctrl+Shift+T',
        translateSelected: 'Ctrl+Shift+S'
      },
      advancedSettings: {
        debugMode: false,
        showOriginalText: false,
        renderType: 'overlay',
        useCorsProxy: false
      }
    };

    // 保存到新格式
    await chrome.storage.local.set({
      'manga-translator-storage': translationState,
      'manga-translator-config': configState
    });

    console.log('数据迁移完成');
  } catch (error) {
    console.error('数据迁移失败:', error);
    throw error;
  }
}

private static migrateHistory(oldHistory: any[]): any[] {
  return oldHistory.map(item => ({
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    imageUrl: item.imageUrl,
    translatedText: item.translatedText,
    timestamp: item.timestamp,
    targetLanguage: 'zh-CN' // 默认值，v0.1没有记录语言
  }));
}

private static migrateProviderConfig(v01Data: any): any {
  const providerType = v01Data.providerType || 'openai';
  const apiKey = v01Data.apiKey || '';
  
  return {
    [providerType]: {
      apiKey,
      apiBaseUrl: this.getDefaultApiBaseUrl(providerType),
      visionModel: this.getDefaultVisionModel(providerType),
      chatModel: this.getDefaultChatModel(providerType),
      temperature: 0.3,
      maxTokens: 1000
    }
  };
}
```

### 2.3 向后兼容性支持

为了确保在迁移过程中的稳定性，实现向后兼容的数据读取：

```typescript
// src/utils/legacy-storage.ts
export class LegacyStorageAdapter {
  /**
   * 兼容性读取配置
   * 优先读取新格式，回退到旧格式
   */
  static async getConfig(): Promise<ConfigState> {
    // 尝试读取新格式
    const newFormat = await chrome.storage.local.get('manga-translator-config');
    if (newFormat['manga-translator-config']) {
      return newFormat['manga-translator-config'];
    }

    // 回退到旧格式
    const oldFormat = await chrome.storage.sync.get(null);
    return this.convertOldConfigFormat(oldFormat);
  }

  /**
   * 兼容性读取翻译状态
   */
  static async getTranslationState(): Promise<TranslationState> {
    const newFormat = await chrome.storage.local.get('manga-translator-storage');
    if (newFormat['manga-translator-storage']) {
      return newFormat['manga-translator-storage'];
    }

    const oldFormat = await chrome.storage.sync.get(null);
    return this.convertOldTranslationFormat(oldFormat);
  }
}
```

## 3. 迁移测试方案

### 3.1 测试数据准备

```typescript
// tests/migration.test.ts
const mockV01Data = {
  apiKey: 'sk-test123',
  providerType: 'openai',
  targetLanguage: 'zh-CN',
  enabled: true,
  mode: 'auto',
  styleLevel: 75,
  translationHistory: [
    {
      imageUrl: 'https://example.com/image1.jpg',
      translatedText: '测试翻译1',
      timestamp: 1640995200000
    },
    {
      imageUrl: 'https://example.com/image2.jpg',
      translatedText: '测试翻译2',
      timestamp: 1640995300000
    }
  ]
};
```

### 3.2 迁移测试用例

```typescript
describe('数据迁移测试', () => {
  beforeEach(async () => {
    // 清理存储
    await chrome.storage.local.clear();
    await chrome.storage.sync.clear();
  });

  test('v0.1到v0.2数据迁移', async () => {
    // 设置v0.1格式数据
    await chrome.storage.sync.set(mockV01Data);

    // 执行迁移
    await DataMigration.checkAndMigrate();

    // 验证迁移结果
    const translationState = await chrome.storage.local.get('manga-translator-storage');
    const configState = await chrome.storage.local.get('manga-translator-config');

    expect(translationState['manga-translator-storage']).toMatchObject({
      enabled: true,
      mode: 'auto',
      targetLanguage: 'zh-CN',
      history: expect.arrayContaining([
        expect.objectContaining({
          imageUrl: 'https://example.com/image1.jpg',
          translatedText: '测试翻译1'
        })
      ])
    });

    expect(configState['manga-translator-config']).toMatchObject({
      providerType: 'openai',
      styleLevel: 75,
      providerConfig: {
        openai: {
          apiKey: 'sk-test123'
        }
      }
    });
  });

  test('新用户无迁移数据', async () => {
    await DataMigration.checkAndMigrate();
    
    const migrationVersion = await chrome.storage.local.get('migration-version');
    expect(migrationVersion['migration-version']).toBe('0.2.0');
  });

  test('重复迁移防护', async () => {
    await chrome.storage.sync.set(mockV01Data);
    
    // 第一次迁移
    await DataMigration.checkAndMigrate();
    const firstResult = await chrome.storage.local.get('manga-translator-storage');
    
    // 第二次迁移（应该跳过）
    await DataMigration.checkAndMigrate();
    const secondResult = await chrome.storage.local.get('manga-translator-storage');
    
    expect(firstResult).toEqual(secondResult);
  });
});
```

## 4. 迁移监控和回滚

### 4.1 迁移状态监控

```typescript
// src/utils/migration-monitor.ts
export class MigrationMonitor {
  static async logMigrationStart(): Promise<void> {
    console.log('开始数据迁移', {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    });
  }

  static async logMigrationSuccess(): Promise<void> {
    console.log('数据迁移成功完成');
    
    // 可选：发送成功统计
    if (process.env.NODE_ENV === 'production') {
      this.sendMigrationStats('success');
    }
  }

  static async logMigrationError(error: Error): Promise<void> {
    console.error('数据迁移失败', error);
    
    // 可选：发送错误统计
    if (process.env.NODE_ENV === 'production') {
      this.sendMigrationStats('error', error.message);
    }
  }

  private static sendMigrationStats(status: string, errorMessage?: string): void {
    // 实现统计发送逻辑
  }
}
```

### 4.2 紧急回滚机制

```typescript
// src/utils/rollback.ts
export class RollbackManager {
  /**
   * 创建迁移前的数据备份
   */
  static async createBackup(): Promise<void> {
    const v01Data = await chrome.storage.sync.get(null);
    await chrome.storage.local.set({
      'migration-backup': {
        data: v01Data,
        timestamp: Date.now()
      }
    });
  }

  /**
   * 回滚到迁移前状态
   */
  static async rollback(): Promise<void> {
    const backup = await chrome.storage.local.get('migration-backup');
    
    if (!backup['migration-backup']) {
      throw new Error('没有找到备份数据');
    }

    // 清除新格式数据
    await chrome.storage.local.remove([
      'manga-translator-storage',
      'manga-translator-config',
      'migration-version'
    ]);

    // 恢复旧格式数据
    await chrome.storage.sync.set(backup['migration-backup'].data);
    
    console.log('数据回滚完成');
  }
}
```

## 5. 部署策略

### 5.1 灰度发布

1. **阶段1**：内部测试版本，验证迁移逻辑
2. **阶段2**：小范围用户测试（5%用户）
3. **阶段3**：扩大测试范围（25%用户）
4. **阶段4**：全量发布

### 5.2 监控指标

- 迁移成功率
- 迁移耗时
- 用户反馈
- 错误率统计

通过这个详细的数据迁移策略，可以确保从v0.1到v0.2的平滑升级，最大程度降低用户影响。
