# 测试策略方案

## 1. 测试概述

### 1.1 测试目标

确保v0.2重构后的漫画翻译插件：
- 功能完整性和正确性
- 性能指标达到预期
- 用户体验流畅
- 兼容性良好
- 稳定性可靠

### 1.2 测试范围

- **单元测试**：核心功能模块
- **集成测试**：组件间交互
- **端到端测试**：完整用户流程
- **性能测试**：响应时间和资源使用
- **兼容性测试**：不同浏览器和网站
- **用户验收测试**：真实使用场景

## 2. 测试环境配置

### 2.1 测试框架选择

```json
{
  "devDependencies": {
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^5.16.5",
    "@testing-library/user-event": "^14.4.3",
    "vitest": "^0.34.0",
    "jsdom": "^22.1.0",
    "chrome-types": "^0.1.0",
    "webextension-polyfill": "^0.10.0"
  }
}
```

### 2.2 测试配置

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*'
      ]
    }
  }
});
```

```typescript
// src/test/setup.ts
import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock Chrome APIs
global.chrome = {
  storage: {
    local: {
      get: vi.fn(),
      set: vi.fn(),
      remove: vi.fn(),
      clear: vi.fn()
    },
    sync: {
      get: vi.fn(),
      set: vi.fn(),
      remove: vi.fn(),
      clear: vi.fn()
    }
  },
  runtime: {
    sendMessage: vi.fn(),
    onMessage: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    }
  },
  tabs: {
    query: vi.fn(),
    sendMessage: vi.fn()
  }
} as any;
```

## 3. 单元测试

### 3.1 状态管理测试

```typescript
// src/stores/__tests__/translation-store.test.ts
import { describe, it, expect, beforeEach } from 'vitest';
import { useTranslationStore } from '../translation-store';

describe('TranslationStore', () => {
  beforeEach(() => {
    useTranslationStore.getState().reset();
  });

  it('应该正确初始化状态', () => {
    const state = useTranslationStore.getState();
    
    expect(state.enabled).toBe(false);
    expect(state.mode).toBe('manual');
    expect(state.targetLanguage).toBe('zh-CN');
    expect(state.processing).toBe(false);
    expect(state.history).toEqual([]);
  });

  it('应该正确切换启用状态', () => {
    const { setEnabled } = useTranslationStore.getState();
    
    setEnabled(true);
    expect(useTranslationStore.getState().enabled).toBe(true);
    
    setEnabled(false);
    expect(useTranslationStore.getState().enabled).toBe(false);
  });

  it('应该正确添加翻译历史', () => {
    const { addHistory } = useTranslationStore.getState();
    
    const historyItem = {
      id: 'test-1',
      imageUrl: 'https://example.com/image.jpg',
      translatedText: '测试翻译',
      timestamp: Date.now(),
      targetLanguage: 'zh-CN'
    };
    
    addHistory(historyItem);
    
    const state = useTranslationStore.getState();
    expect(state.history).toHaveLength(1);
    expect(state.history[0]).toEqual(historyItem);
  });
});
```

### 3.2 API提供者测试

```typescript
// src/api/providers/__tests__/openai-provider.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { OpenAIProvider } from '../openai-provider';

describe('OpenAIProvider', () => {
  let provider: OpenAIProvider;
  
  beforeEach(() => {
    provider = new OpenAIProvider({
      apiKey: 'test-key',
      apiBaseUrl: 'https://api.openai.com/v1',
      visionModel: 'gpt-4-vision-preview',
      chatModel: 'gpt-3.5-turbo',
      temperature: 0.3,
      maxTokens: 1000
    });
  });

  it('应该正确初始化配置', () => {
    expect(provider.config.apiKey).toBe('test-key');
    expect(provider.config.visionModel).toBe('gpt-4-vision-preview');
  });

  it('应该正确处理图像翻译请求', async () => {
    // Mock fetch
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        choices: [{
          message: {
            content: JSON.stringify({
              textAreas: [
                {
                  x: 100, y: 100, width: 200, height: 50,
                  text: 'Hello', type: 'bubble', order: 1
                }
              ],
              translations: ['你好']
            })
          }
        }]
      })
    });

    const result = await provider.translateImage(
      'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...',
      'zh-CN'
    );

    expect(result.textAreas).toHaveLength(1);
    expect(result.translations).toEqual(['你好']);
    expect(result.targetLanguage).toBe('zh-CN');
  });

  it('应该正确处理API错误', async () => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: false,
      status: 401,
      statusText: 'Unauthorized'
    });

    await expect(
      provider.translateImage('test-image', 'zh-CN')
    ).rejects.toThrow('API请求失败: 401');
  });
});
```

### 3.3 工具函数测试

```typescript
// src/utils/__tests__/image-process.test.ts
import { describe, it, expect } from 'vitest';
import { ImageProcessor } from '../image-process';

describe('ImageProcessor', () => {
  it('应该正确检测图像格式', () => {
    const jpegData = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...';
    const pngData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    
    expect(ImageProcessor.getImageFormat(jpegData)).toBe('jpeg');
    expect(ImageProcessor.getImageFormat(pngData)).toBe('png');
  });

  it('应该正确压缩图像', async () => {
    const canvas = document.createElement('canvas');
    canvas.width = 1000;
    canvas.height = 1000;
    
    const ctx = canvas.getContext('2d')!;
    ctx.fillStyle = 'red';
    ctx.fillRect(0, 0, 1000, 1000);
    
    const originalData = canvas.toDataURL('image/jpeg', 1.0);
    const compressedData = await ImageProcessor.compressImage(originalData, 0.5);
    
    expect(compressedData.length).toBeLessThan(originalData.length);
  });
});
```

## 4. 集成测试

### 4.1 组件集成测试

```typescript
// src/components/__tests__/popup-app.test.tsx
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PopupApp } from '../Popup/PopupApp';

// Mock stores
vi.mock('../../stores/translation-store');
vi.mock('../../stores/config-store');

describe('PopupApp集成测试', () => {
  it('应该正确渲染主界面', () => {
    render(<PopupApp />);
    
    expect(screen.getByText('漫画翻译助手')).toBeInTheDocument();
    expect(screen.getByRole('switch')).toBeInTheDocument();
    expect(screen.getByText('目标语言')).toBeInTheDocument();
  });

  it('应该正确处理启用/禁用切换', async () => {
    const mockSetEnabled = vi.fn();
    vi.mocked(useTranslationStore).mockReturnValue({
      enabled: false,
      setEnabled: mockSetEnabled
    });

    render(<PopupApp />);
    
    const toggleSwitch = screen.getByRole('switch');
    fireEvent.click(toggleSwitch);
    
    await waitFor(() => {
      expect(mockSetEnabled).toHaveBeenCalledWith(true);
    });
  });

  it('应该正确显示翻译历史', () => {
    vi.mocked(useTranslationStore).mockReturnValue({
      history: [
        {
          id: '1',
          imageUrl: 'https://example.com/image1.jpg',
          translatedText: '测试翻译1',
          timestamp: Date.now(),
          targetLanguage: 'zh-CN'
        }
      ]
    });

    render(<PopupApp />);
    
    expect(screen.getByText('测试翻译1')).toBeInTheDocument();
  });
});
```

### 4.2 API集成测试

```typescript
// src/api/__tests__/translation-service.test.ts
import { describe, it, expect, vi } from 'vitest';
import { TranslationService } from '../translation-service';

describe('TranslationService集成测试', () => {
  it('应该正确处理完整翻译流程', async () => {
    const service = new TranslationService();
    
    // Mock provider
    const mockProvider = {
      translateImage: vi.fn().mockResolvedValue({
        textAreas: [
          { x: 100, y: 100, width: 200, height: 50, text: 'Hello', type: 'bubble', order: 1 }
        ],
        translations: ['你好'],
        imageData: 'processed-image-data',
        targetLanguage: 'zh-CN',
        timestamp: Date.now()
      })
    };

    service.setProvider(mockProvider);

    const result = await service.translateImage(
      'data:image/jpeg;base64,test-image',
      'zh-CN'
    );

    expect(result.textAreas).toHaveLength(1);
    expect(result.translations).toEqual(['你好']);
    expect(mockProvider.translateImage).toHaveBeenCalledWith(
      'data:image/jpeg;base64,test-image',
      'zh-CN'
    );
  });
});
```

## 5. 端到端测试

### 5.1 用户流程测试

```typescript
// e2e/translation-flow.test.ts
import { test, expect } from '@playwright/test';

test.describe('翻译流程测试', () => {
  test('完整翻译流程', async ({ page, context }) => {
    // 加载插件
    const extensionId = await loadExtension(context);
    
    // 打开测试页面
    await page.goto('https://example-manga-site.com');
    
    // 打开插件弹窗
    await page.goto(`chrome-extension://${extensionId}/popup.html`);
    
    // 启用翻译
    await page.click('[data-testid="enable-toggle"]');
    
    // 设置目标语言
    await page.selectOption('[data-testid="language-select"]', 'zh-CN');
    
    // 返回内容页面
    await page.goto('https://example-manga-site.com');
    
    // 点击图像触发翻译
    await page.click('img[src*="manga"]');
    
    // 等待翻译完成
    await page.waitForSelector('[data-testid="translation-overlay"]');
    
    // 验证翻译结果
    const translationText = await page.textContent('[data-testid="translation-text"]');
    expect(translationText).toBeTruthy();
  });

  test('错误处理流程', async ({ page, context }) => {
    const extensionId = await loadExtension(context);
    
    // 设置无效API密钥
    await page.goto(`chrome-extension://${extensionId}/options.html`);
    await page.fill('[data-testid="api-key-input"]', 'invalid-key');
    await page.click('[data-testid="save-button"]');
    
    // 尝试翻译
    await page.goto('https://example-manga-site.com');
    await page.click('img[src*="manga"]');
    
    // 验证错误提示
    await page.waitForSelector('[data-testid="error-toast"]');
    const errorMessage = await page.textContent('[data-testid="error-message"]');
    expect(errorMessage).toContain('API密钥无效');
  });
});
```

## 6. 性能测试

### 6.1 响应时间测试

```typescript
// src/test/performance.test.ts
import { describe, it, expect } from 'vitest';
import { performance } from 'perf_hooks';

describe('性能测试', () => {
  it('翻译响应时间应小于3秒', async () => {
    const startTime = performance.now();
    
    // 执行翻译
    const result = await translateImage(testImageData, 'zh-CN');
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    expect(duration).toBeLessThan(3000); // 3秒
    expect(result).toBeDefined();
  });

  it('状态更新应该是同步的', () => {
    const startTime = performance.now();
    
    const { setEnabled } = useTranslationStore.getState();
    setEnabled(true);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    expect(duration).toBeLessThan(10); // 10毫秒
    expect(useTranslationStore.getState().enabled).toBe(true);
  });
});
```

### 6.2 内存使用测试

```typescript
// src/test/memory.test.ts
describe('内存使用测试', () => {
  it('应该正确清理事件监听器', () => {
    const { unmount } = render(<ContentScript />);
    
    const initialListeners = getEventListenerCount();
    unmount();
    const finalListeners = getEventListenerCount();
    
    expect(finalListeners).toBeLessThanOrEqual(initialListeners);
  });

  it('缓存大小应该有限制', async () => {
    const cache = new TranslationCache();
    
    // 添加大量缓存项
    for (let i = 0; i < 200; i++) {
      await cache.set(`key-${i}`, `value-${i}`);
    }
    
    const cacheSize = await cache.size();
    expect(cacheSize).toBeLessThanOrEqual(100); // 最大100项
  });
});
```

## 7. 兼容性测试

### 7.1 浏览器兼容性

```typescript
// src/test/compatibility.test.ts
describe('浏览器兼容性测试', () => {
  const browsers = ['chrome', 'edge', 'firefox'];
  
  browsers.forEach(browser => {
    it(`应该在${browser}中正常工作`, async () => {
      const result = await runInBrowser(browser, async () => {
        // 测试核心功能
        const translation = await translateImage(testImage, 'zh-CN');
        return translation.translations.length > 0;
      });
      
      expect(result).toBe(true);
    });
  });
});
```

### 7.2 网站兼容性

```typescript
const testSites = [
  'https://mangadex.org',
  'https://manganelo.com',
  'https://readmanga.live'
];

testSites.forEach(site => {
  test(`应该在${site}正常工作`, async ({ page }) => {
    await page.goto(site);
    
    // 查找漫画图像
    const images = await page.$$('img[src*="manga"], img[src*="chapter"]');
    
    if (images.length > 0) {
      await images[0].click();
      
      // 验证翻译功能
      await page.waitForSelector('[data-testid="translation-overlay"]', {
        timeout: 5000
      });
      
      const overlay = await page.$('[data-testid="translation-overlay"]');
      expect(overlay).toBeTruthy();
    }
  });
});
```

## 8. 测试数据管理

### 8.1 测试数据集

```typescript
// src/test/fixtures/test-data.ts
export const testImages = {
  simple: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...',
  complex: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
  large: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...'
};

export const expectedTranslations = {
  simple: ['你好'],
  complex: ['复杂文本翻译'],
  large: ['大图像翻译结果']
};
```

### 8.2 Mock数据生成

```typescript
// src/test/utils/mock-generator.ts
export class MockDataGenerator {
  static generateTranslationResult(overrides = {}): TranslationResult {
    return {
      textAreas: [
        {
          x: 100,
          y: 100,
          width: 200,
          height: 50,
          text: 'Mock text',
          type: 'bubble',
          order: 1
        }
      ],
      translations: ['模拟翻译'],
      imageData: 'mock-image-data',
      targetLanguage: 'zh-CN',
      timestamp: Date.now(),
      ...overrides
    };
  }

  static generateConfigState(overrides = {}): ConfigState {
    return {
      providerType: 'openai',
      providerConfig: {
        openai: {
          apiKey: 'mock-api-key',
          apiBaseUrl: 'https://api.openai.com/v1',
          visionModel: 'gpt-4-vision-preview',
          chatModel: 'gpt-3.5-turbo',
          temperature: 0.3,
          maxTokens: 1000
        }
      },
      styleLevel: 50,
      shortcuts: {
        toggleTranslation: 'Ctrl+Shift+T',
        translateSelected: 'Ctrl+Shift+S'
      },
      advancedSettings: {
        debugMode: false,
        showOriginalText: false,
        renderType: 'overlay',
        useCorsProxy: false
      },
      ...overrides
    };
  }
}
```

## 9. 持续集成

### 9.1 GitHub Actions配置

```yaml
# .github/workflows/test.yml
name: 测试流水线

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: 设置Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: 安装依赖
      run: npm ci
    
    - name: 运行单元测试
      run: npm run test:unit
    
    - name: 运行集成测试
      run: npm run test:integration
    
    - name: 生成覆盖率报告
      run: npm run test:coverage
    
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
```

### 9.2 测试脚本配置

```json
{
  "scripts": {
    "test": "vitest",
    "test:unit": "vitest run src/**/*.test.ts",
    "test:integration": "vitest run src/**/*.integration.test.ts",
    "test:e2e": "playwright test",
    "test:coverage": "vitest run --coverage",
    "test:watch": "vitest --watch"
  }
}
```

通过这个全面的测试策略，可以确保v0.2重构的质量和稳定性，为用户提供可靠的翻译体验。
