# 漫画翻译插件重构方案

## 1. 项目现状分析

### 1.1 当前技术栈

- **构建工具**: Vite + React
- **样式框架**: TailwindCSS v3
- **状态管理**: 原生 React 状态 + Chrome Storage
- **API 层**: 自定义 Provider 抽象层
- **UI 组件**: 基础 HTML + TailwindCSS

### 1.2 已完成功能

- ✅ 多 API 提供者支持（OpenAI、DeepSeek、Claude、Qwen）
- ✅ 基础的文字检测和翻译功能
- ✅ Chrome 插件基础架构
- ✅ 基本的用户配置界面
- ✅ 手动和自动翻译模式

### 1.3 存在的问题

- ❌ UI 组件库缺失，界面不够现代化
- ❌ 状态管理分散，缺乏统一管理
- ❌ 错误处理不完善
- ❌ 性能优化不足
- ❌ 用户体验有待提升
- ❌ 开发体验不够友好

## 2. 重构目标

### 2.1 核心目标

1. **现代化 UI**: 集成 shadcn/ui 组件库，提供一致美观的用户界面
2. **优化状态管理**: 引入 Zustand 进行统一状态管理
3. **提升性能**: 优化缓存策略和 API 调用
4. **改善用户体验**: 增强交互设计和错误处理
5. **优化开发体验**: 完善开发工具和调试功能

### 2.2 技术目标

- 保持 Vite 构建工具的优势
- 引入现代化的 React 生态工具
- 建立可扩展的架构设计
- 确保向后兼容性

## 3. 技术选型

### 3.1 UI 框架

**选择**: shadcn/ui + TailwindCSS v4
**理由**:

- 提供丰富的现代化组件
- 高度可定制化
- 优秀的 TypeScript 支持
- 与 TailwindCSS 完美集成

### 3.2 状态管理

**选择**: Zustand + React Query
**理由**:

- Zustand 轻量级，适合 Chrome 插件
- React Query 处理 API 状态，提供缓存和同步
- 优秀的开发体验和调试工具

### 3.3 构建工具

**保持**: Vite
**优化**: 自定义插件和配置优化

### 3.4 开发工具

**新增**:

- TypeScript 严格模式
- ESLint + Prettier
- Husky + lint-staged
- 开发调试工具

## 4. 架构设计

### 4.1 分层架构

```
┌─────────────────────────────────────┐
│           UI Layer (shadcn/ui)      │
├─────────────────────────────────────┤
│        State Layer (Zustand)        │
├─────────────────────────────────────┤
│      API Layer (React Query)        │
├─────────────────────────────────────┤
│    Provider Layer (AI Providers)    │
├─────────────────────────────────────┤
│    Chrome API Layer (Extension)     │
└─────────────────────────────────────┘
```

### 4.2 模块划分

```
src/
├── components/          # UI组件
│   ├── ui/             # shadcn/ui组件
│   ├── Popup/          # 弹出窗口组件
│   ├── Options/        # 选项页面组件
│   └── Content/        # 内容脚本组件
├── stores/             # Zustand状态管理
├── hooks/              # 自定义Hooks
├── api/                # API层
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
└── constants/          # 常量定义
```

## 5. 实施计划

### 5.1 第一阶段：基础现代化（1-2 周）

#### 5.1.1 环境准备

- [ ] 升级 TailwindCSS 到 v4
- [ ] 集成 shadcn/ui 组件库
- [ ] 配置 TypeScript 严格模式
- [ ] 设置 ESLint 和 Prettier
- [ ] 配置 Husky 和 lint-staged

#### 5.1.2 状态管理重构

- [ ] 引入 Zustand
- [ ] 设计状态结构
- [ ] 实现持久化中间件
- [ ] 迁移现有状态逻辑

#### 5.1.3 UI 组件重构

- [ ] 创建 shadcn/ui 主题配置
- [ ] 重构弹出窗口组件
- [ ] 重构选项页面组件
- [ ] 优化响应式设计

### 5.2 第二阶段：功能增强（2-3 周）

#### 5.2.1 API 层优化

- [ ] 引入 React Query
- [ ] 重构 API 调用逻辑
- [ ] 实现智能缓存策略
- [ ] 优化错误处理

#### 5.2.2 性能优化

- [ ] 实现 Web Worker
- [ ] 优化图像处理
- [ ] 添加智能预加载
- [ ] 实现虚拟滚动

#### 5.2.3 用户体验提升

- [ ] 添加加载状态
- [ ] 优化错误提示
- [ ] 实现翻译历史
- [ ] 添加快捷键支持

### 5.3 第三阶段：高级功能（1-2 周）

#### 5.3.1 高级功能

- [ ] 批量翻译功能
- [ ] 翻译质量评估
- [ ] 自定义翻译风格
- [ ] 导出翻译结果

#### 5.3.2 开发体验

- [ ] 完善调试工具
- [ ] 添加性能监控
- [ ] 优化热重载
- [ ] 完善文档

## 6. 详细实施指南

### 6.1 环境配置

#### 6.1.1 依赖安装

```bash
# 核心依赖
npm install zustand @tanstack/react-query
npm install @radix-ui/react-* class-variance-authority clsx tailwind-merge

# 开发依赖
npm install -D @types/node typescript
npm install -D eslint prettier eslint-config-prettier
npm install -D husky lint-staged
```

#### 6.1.2 shadcn/ui 配置

```bash
# 初始化shadcn/ui
npx shadcn@latest init

# 安装常用组件
npx shadcn@latest add button card input select
npx shadcn@latest add progress badge toast
npx shadcn@latest add dialog sheet tabs
```

#### 6.1.3 TypeScript 配置

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

### 6.2 状态管理设计

#### 6.2.1 Zustand Store 设计

```typescript
// stores/translation.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface TranslationState {
  enabled: boolean;
  mode: "manual" | "auto";
  targetLanguage: string;
  processing: boolean;
  currentImage: string | null;
}

interface TranslationActions {
  setEnabled: (enabled: boolean) => void;
  setMode: (mode: "manual" | "auto") => void;
  setTargetLanguage: (language: string) => void;
  setProcessing: (processing: boolean) => void;
  setCurrentImage: (image: string | null) => void;
}

export const useTranslationStore = create<
  TranslationState & TranslationActions
>()(
  persist(
    (set) => ({
      enabled: false,
      mode: "manual",
      targetLanguage: "zh-CN",
      processing: false,
      currentImage: null,

      setEnabled: (enabled) => set({ enabled }),
      setMode: (mode) => set({ mode }),
      setTargetLanguage: (targetLanguage) => set({ targetLanguage }),
      setProcessing: (processing) => set({ processing }),
      setCurrentImage: (currentImage) => set({ currentImage }),
    }),
    {
      name: "manga-translator-storage",
      partialize: (state) => ({
        enabled: state.enabled,
        mode: state.mode,
        targetLanguage: state.targetLanguage,
      }),
    }
  )
);
```

#### 6.2.2 配置 Store 设计

```typescript
// stores/config.ts
interface ConfigState {
  providerType: string;
  apiKey: string;
  settings: Record<string, any>;
}

interface ConfigActions {
  setProviderType: (type: string) => void;
  setApiKey: (key: string) => void;
  updateSettings: (settings: Record<string, any>) => void;
}

export const useConfigStore = create<ConfigState & ConfigActions>()(
  persist(
    (set) => ({
      providerType: "openai",
      apiKey: "",
      settings: {},

      setProviderType: (providerType) => set({ providerType }),
      setApiKey: (apiKey) => set({ apiKey }),
      updateSettings: (settings) =>
        set((state) => ({
          settings: { ...state.settings, ...settings },
        })),
    }),
    {
      name: "manga-translator-config",
    }
  )
);
```

### 6.3 UI 组件重构

#### 6.3.1 主题配置

```typescript
// lib/utils.ts
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
```

```typescript
// components/ui/theme-provider.tsx
import { createContext, useContext, useEffect, useState } from "react";

type Theme = "dark" | "light" | "system";

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
};

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
};

const initialState: ThemeProviderState = {
  theme: "system",
  setTheme: () => null,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "manga-translator-ui-theme",
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(
    () => (localStorage.getItem(storageKey) as Theme) || defaultTheme
  );

  useEffect(() => {
    const root = window.document.documentElement;
    root.classList.remove("light", "dark");

    if (theme === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)")
        .matches
        ? "dark"
        : "light";
      root.classList.add(systemTheme);
      return;
    }

    root.classList.add(theme);
  }, [theme]);

  const value = {
    theme,
    setTheme: (theme: Theme) => {
      localStorage.setItem(storageKey, theme);
      setTheme(theme);
    },
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider");

  return context;
};
```

#### 6.3.2 弹出窗口重构

```typescript
// components/Popup/PopupApp.tsx
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslationStore } from "@/stores/translation";
import { useConfigStore } from "@/stores/config";

export function PopupApp() {
  const {
    enabled,
    mode,
    targetLanguage,
    setEnabled,
    setMode,
    setTargetLanguage,
  } = useTranslationStore();
  const { providerType, apiKey, setProviderType, setApiKey } = useConfigStore();

  return (
    <div className="w-80 p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">漫画翻译助手</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* API配置 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">API提供者</label>
            <Select value={providerType} onValueChange={setProviderType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI</SelectItem>
                <SelectItem value="deepseek">DeepSeek</SelectItem>
                <SelectItem value="claude">Claude</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 翻译开关 */}
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">启用翻译</label>
            <Switch checked={enabled} onCheckedChange={setEnabled} />
          </div>

          {/* 翻译模式 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">翻译模式</label>
            <Select
              value={mode}
              onValueChange={(value: "manual" | "auto") => setMode(value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="manual">手动模式</SelectItem>
                <SelectItem value="auto">自动模式</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 目标语言 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">目标语言</label>
            <Select value={targetLanguage} onValueChange={setTargetLanguage}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="zh-CN">简体中文</SelectItem>
                <SelectItem value="zh-TW">繁体中文</SelectItem>
                <SelectItem value="en">英语</SelectItem>
                <SelectItem value="ja">日语</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

### 6.4 API 层优化

#### 6.4.1 React Query 配置

```typescript
// lib/query-client.ts
import { QueryClient } from "@tanstack/react-query";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
    },
  },
});
```

#### 6.4.2 翻译 API Hooks

```typescript
// hooks/useTranslation.ts
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslationStore } from "@/stores/translation";
import { useConfigStore } from "@/stores/config";

export function useTranslation(imageData: string | null) {
  const { targetLanguage } = useTranslationStore();
  const { providerType, apiKey } = useConfigStore();

  return useQuery({
    queryKey: ["translation", imageData, targetLanguage, providerType],
    queryFn: () => translateImage(imageData!, targetLanguage, providerType),
    enabled: !!imageData && !!apiKey,
    staleTime: 10 * 60 * 1000, // 10分钟
  });
}

export function useTranslateMutation() {
  const queryClient = useQueryClient();
  const { targetLanguage } = useTranslationStore();
  const { providerType } = useConfigStore();

  return useMutation({
    mutationFn: ({ imageData }: { imageData: string }) =>
      translateImage(imageData, targetLanguage, providerType),
    onSuccess: (data, variables) => {
      // 更新缓存
      queryClient.setQueryData(
        ["translation", variables.imageData, targetLanguage, providerType],
        data
      );
    },
  });
}
```

### 6.5 性能优化

#### 6.5.1 Web Worker 集成

```typescript
// workers/image-processor.ts
self.onmessage = async (e) => {
  const { type, data } = e.data;

  switch (type) {
    case "PREPROCESS_IMAGE":
      try {
        const processed = await preprocessImage(data.imageData);
        self.postMessage({ type: "PREPROCESS_COMPLETE", result: processed });
      } catch (error) {
        self.postMessage({ type: "PREPROCESS_ERROR", error: error.message });
      }
      break;

    case "DETECT_TEXT":
      try {
        const textAreas = await detectTextAreas(data.imageData);
        self.postMessage({ type: "DETECT_COMPLETE", result: textAreas });
      } catch (error) {
        self.postMessage({ type: "DETECT_ERROR", error: error.message });
      }
      break;
  }
};
```

#### 6.5.2 智能缓存

```typescript
// utils/cache.ts
class SmartCache {
  private memoryCache = new Map<string, any>();
  private indexedDB: IDBDatabase | null = null;

  async init() {
    return new Promise<void>((resolve, reject) => {
      const request = indexedDB.open("MangaTranslatorCache", 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.indexedDB = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains("translations")) {
          db.createObjectStore("translations", { keyPath: "key" });
        }
      };
    });
  }

  async get(key: string) {
    // 先查内存缓存
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key);
    }

    // 再查IndexedDB
    if (this.indexedDB) {
      const transaction = this.indexedDB.transaction(
        ["translations"],
        "readonly"
      );
      const store = transaction.objectStore("translations");
      const request = store.get(key);

      return new Promise((resolve) => {
        request.onsuccess = () => {
          const result = request.result?.value;
          if (result) {
            this.memoryCache.set(key, result);
          }
          resolve(result);
        };
        request.onerror = () => resolve(null);
      });
    }

    return null;
  }

  async set(key: string, value: any) {
    this.memoryCache.set(key, value);

    if (this.indexedDB) {
      const transaction = this.indexedDB.transaction(
        ["translations"],
        "readwrite"
      );
      const store = transaction.objectStore("translations");
      store.put({ key, value, timestamp: Date.now() });
    }
  }
}

export const smartCache = new SmartCache();
```

## 7. 测试策略

### 7.1 单元测试

- 使用 Vitest 进行单元测试
- 测试状态管理逻辑
- 测试工具函数
- 测试 API 调用

### 7.2 集成测试

- 测试完整翻译流程
- 测试 Chrome 插件 API 集成
- 测试错误处理

### 7.3 端到端测试

- 使用 Playwright 进行 E2E 测试
- 测试用户交互流程
- 测试不同网站兼容性

## 8. 部署和发布

### 8.1 构建优化

- 优化打包大小
- 代码分割
- 资源压缩

### 8.2 发布流程

- 自动化构建
- 版本管理
- Chrome Web Store 发布

## 9. 监控和维护

### 9.1 性能监控

- 翻译响应时间
- 内存使用情况
- 错误率统计

### 9.2 用户反馈

- 错误报告收集
- 用户行为分析
- 功能使用统计

## 10. 风险评估

### 10.1 技术风险

- **风险**: shadcn/ui 在 Chrome 插件中的兼容性问题
- **缓解**: 充分测试，准备回退方案

### 10.2 性能风险

- **风险**: 新架构可能影响性能
- **缓解**: 性能基准测试，渐进式优化

### 10.3 用户接受度风险

- **风险**: 用户不适应新界面
- **缓解**: 用户测试，渐进式发布

## 11. 成功指标

### 11.1 技术指标

- 构建时间减少 50%
- 包大小减少 30%
- 错误率降低 80%

### 11.2 用户体验指标

- 翻译响应时间 < 3 秒
- 用户满意度 > 4.5/5
- 功能使用率提升 50%

### 11.3 开发体验指标

- 开发效率提升 40%
- 代码质量评分 > 90%
- 测试覆盖率 > 80%

## 12. 总结

本重构方案采用渐进式现代化策略，在保持现有功能的基础上，引入现代化的技术栈和最佳实践。通过分阶段实施，可以确保项目的稳定性和可维护性，同时显著提升用户体验和开发效率。

重构完成后，项目将具备：

- 现代化的用户界面
- 高效的状态管理
- 优秀的性能表现
- 完善的错误处理
- 良好的开发体验

这将为项目的长期发展奠定坚实的基础。
