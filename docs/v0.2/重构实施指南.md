# 漫画翻译插件重构实施指南

## 1. 环境准备

### 1.1 依赖安装

```bash
# 安装核心依赖
npm install zustand @tanstack/react-query
npm install @radix-ui/react-slot class-variance-authority clsx tailwind-merge
npm install lucide-react

# 安装开发依赖
npm install -D @types/node typescript
npm install -D eslint prettier eslint-config-prettier
npm install -D husky lint-staged
```

### 1.2 shadcn/ui 初始化

```bash
# 初始化shadcn/ui
npx shadcn@latest init

# 选择配置：
# - Style: Default
# - Base color: Slate
# - CSS variables: Yes
# - React Server Components: No
# - Components directory: @/components/ui
# - Utils directory: @/lib/utils
# - Include example components: No

# 安装基础组件
npx shadcn@latest add button card input select switch
npx shadcn@latest add progress badge toast dialog
npx shadcn@latest add sheet tabs textarea
```

### 1.3 TypeScript 配置

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

## 2. 状态管理重构

### 2.1 创建 Store 文件

```typescript
// src/stores/translation.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface TranslationState {
  enabled: boolean;
  mode: "manual" | "auto";
  targetLanguage: string;
  processing: boolean;
  currentImage: string | null;
  history: Array<{
    id: string;
    imageUrl: string;
    translatedText: string;
    timestamp: number;
  }>;
}

interface TranslationActions {
  setEnabled: (enabled: boolean) => void;
  setMode: (mode: "manual" | "auto") => void;
  setTargetLanguage: (language: string) => void;
  setProcessing: (processing: boolean) => void;
  setCurrentImage: (image: string | null) => void;
  addToHistory: (
    item: Omit<TranslationState["history"][0], "id" | "timestamp">
  ) => void;
  clearHistory: () => void;
}

export const useTranslationStore = create<
  TranslationState & TranslationActions
>()(
  persist(
    (set, get) => ({
      enabled: false,
      mode: "manual",
      targetLanguage: "zh-CN",
      processing: false,
      currentImage: null,
      history: [],

      setEnabled: (enabled) => set({ enabled }),
      setMode: (mode) => set({ mode }),
      setTargetLanguage: (targetLanguage) => set({ targetLanguage }),
      setProcessing: (processing) => set({ processing }),
      setCurrentImage: (currentImage) => set({ currentImage }),

      addToHistory: (item) =>
        set((state) => ({
          history: [
            {
              ...item,
              id: Date.now().toString(),
              timestamp: Date.now(),
            },
            ...state.history.slice(0, 99), // 保留最近100条
          ],
        })),

      clearHistory: () => set({ history: [] }),
    }),
    {
      name: "manga-translator-storage",
      partialize: (state) => ({
        enabled: state.enabled,
        mode: state.mode,
        targetLanguage: state.targetLanguage,
        history: state.history,
      }),
    }
  )
);
```

```typescript
// src/stores/config.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface ConfigState {
  providerType: string;
  providerConfig: Record<string, any>;
  styleLevel: number;
  shortcuts: Record<string, string>;
  advancedSettings: {
    debugMode: boolean;
    showOriginalText: boolean;
    renderType: "overlay" | "replace";
    useCorsProxy: boolean;
  };
}

interface ConfigActions {
  setProviderType: (type: string) => void;
  updateProviderConfig: (provider: string, config: any) => void;
  setStyleLevel: (level: number) => void;
  setShortcuts: (shortcuts: Record<string, string>) => void;
  updateAdvancedSettings: (
    settings: Partial<ConfigState["advancedSettings"]>
  ) => void;
}

export const useConfigStore = create<ConfigState & ConfigActions>()(
  persist(
    (set) => ({
      providerType: "openai",
      providerConfig: {
        openai: {
          apiKey: "",
          apiBaseUrl: "https://api.openai.com/v1",
          visionModel: "gpt-4-vision-preview",
          chatModel: "gpt-3.5-turbo",
          temperature: 0.3,
          maxTokens: 1000,
        },
        deepseek: {
          apiKey: "",
          apiBaseUrl: "https://api.deepseek.com/v1",
          visionModel: "deepseek-vl",
          chatModel: "deepseek-chat",
          temperature: 0.3,
          maxTokens: 1000,
        },
        claude: {
          apiKey: "",
          apiBaseUrl: "https://api.anthropic.com/v1",
          visionModel: "claude-3-opus-20240229",
          chatModel: "claude-3-haiku-20240307",
          temperature: 0.3,
          maxTokens: 1000,
        },
      },
      styleLevel: 50,
      shortcuts: {
        toggleTranslation: "Ctrl+Shift+T",
        translateSelected: "Ctrl+Shift+S",
      },
      advancedSettings: {
        debugMode: false,
        showOriginalText: false,
        renderType: "overlay",
        useCorsProxy: false,
      },

      setProviderType: (providerType) => set({ providerType }),
      updateProviderConfig: (provider, config) =>
        set((state) => ({
          providerConfig: {
            ...state.providerConfig,
            [provider]: { ...state.providerConfig[provider], ...config },
          },
        })),
      setStyleLevel: (styleLevel) => set({ styleLevel }),
      setShortcuts: (shortcuts) => set({ shortcuts }),
      updateAdvancedSettings: (settings) =>
        set((state) => ({
          advancedSettings: { ...state.advancedSettings, ...settings },
        })),
    }),
    {
      name: "manga-translator-config",
    }
  )
);
```

### 2.2 创建自定义 Hooks

```typescript
// src/hooks/useTranslation.ts
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslationStore } from "@/stores/translation";
import { useConfigStore } from "@/stores/config";
import { translateImage } from "@/api/translation";

export function useTranslation(imageData: string | null) {
  const { targetLanguage } = useTranslationStore();
  const { providerType, providerConfig } = useConfigStore();
  const config = providerConfig[providerType];

  return useQuery({
    queryKey: ["translation", imageData, targetLanguage, providerType],
    queryFn: () =>
      translateImage(imageData!, targetLanguage, providerType, config),
    enabled: !!imageData && !!config?.apiKey,
    staleTime: 10 * 60 * 1000, // 10分钟
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

export function useTranslateMutation() {
  const queryClient = useQueryClient();
  const { targetLanguage, addToHistory } = useTranslationStore();
  const { providerType, providerConfig } = useConfigStore();
  const config = providerConfig[providerType];

  return useMutation({
    mutationFn: ({
      imageData,
      imageUrl,
    }: {
      imageData: string;
      imageUrl: string;
    }) => translateImage(imageData, targetLanguage, providerType, config),
    onSuccess: (data, variables) => {
      // 更新缓存
      queryClient.setQueryData(
        ["translation", variables.imageData, targetLanguage, providerType],
        data
      );

      // 添加到历史
      addToHistory({
        imageUrl: variables.imageUrl,
        translatedText: data.translations.join("\n"),
      });
    },
  });
}
```

## 3. UI 组件重构

### 3.1 弹出窗口组件

```typescript
// src/components/Popup/PopupApp.tsx
import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useTranslationStore } from "@/stores/translation";
import { useConfigStore } from "@/stores/config";
import { Settings, History, HelpCircle } from "lucide-react";

export function PopupApp() {
  const {
    enabled,
    mode,
    targetLanguage,
    processing,
    setEnabled,
    setMode,
    setTargetLanguage,
  } = useTranslationStore();

  const {
    providerType,
    providerConfig,
    setProviderType,
    updateProviderConfig,
  } = useConfigStore();

  const currentConfig = providerConfig[providerType];

  const handleApiKeyChange = (apiKey: string) => {
    updateProviderConfig(providerType, { apiKey });
  };

  const openOptionsPage = () => {
    chrome.runtime.openOptionsPage();
  };

  const openHistory = () => {
    // 实现历史记录查看功能
  };

  return (
    <div className="w-80 p-4 space-y-4 bg-background">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Badge variant="secondary">漫画翻译助手</Badge>
            {processing && (
              <Badge variant="outline" className="animate-pulse">
                处理中
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* API配置 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">API提供者</label>
            <Select value={providerType} onValueChange={setProviderType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI</SelectItem>
                <SelectItem value="deepseek">DeepSeek</SelectItem>
                <SelectItem value="claude">Claude</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* API密钥 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">API密钥</label>
            <input
              type="password"
              value={currentConfig?.apiKey || ""}
              onChange={(e) => handleApiKeyChange(e.target.value)}
              placeholder="请输入API密钥"
              className="w-full px-3 py-2 border border-input rounded-md text-sm"
            />
          </div>

          <Separator />

          {/* 翻译开关 */}
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">启用翻译</label>
            <Switch checked={enabled} onCheckedChange={setEnabled} />
          </div>

          {/* 翻译模式 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">翻译模式</label>
            <Select
              value={mode}
              onValueChange={(value: "manual" | "auto") => setMode(value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="manual">手动模式</SelectItem>
                <SelectItem value="auto">自动模式</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 目标语言 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">目标语言</label>
            <Select value={targetLanguage} onValueChange={setTargetLanguage}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="zh-CN">简体中文</SelectItem>
                <SelectItem value="zh-TW">繁体中文</SelectItem>
                <SelectItem value="en">英语</SelectItem>
                <SelectItem value="ja">日语</SelectItem>
                <SelectItem value="ko">韩语</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={openOptionsPage}
          className="flex-1"
        >
          <Settings className="w-4 h-4 mr-2" />
          高级设置
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={openHistory}
          className="flex-1"
        >
          <History className="w-4 h-4 mr-2" />
          翻译历史
        </Button>
        <Button variant="outline" size="sm" className="flex-1">
          <HelpCircle className="w-4 h-4 mr-2" />
          帮助
        </Button>
      </div>
    </div>
  );
}
```

### 3.2 选项页面组件

```typescript
// src/components/Options/OptionsApp.tsx
import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useConfigStore } from "@/stores/config";
import { useTranslationStore } from "@/stores/translation";

export function OptionsApp() {
  const {
    providerType,
    providerConfig,
    styleLevel,
    shortcuts,
    advancedSettings,
    setProviderType,
    updateProviderConfig,
    setStyleLevel,
    setShortcuts,
    updateAdvancedSettings,
  } = useConfigStore();

  const { clearHistory } = useTranslationStore();

  const currentConfig = providerConfig[providerType];

  const handleConfigChange = (key: string, value: any) => {
    updateProviderConfig(providerType, { [key]: value });
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">漫画翻译助手设置</h1>
        <p className="text-muted-foreground">配置翻译参数和个性化设置</p>
      </div>

      <Tabs defaultValue="api" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="api">API设置</TabsTrigger>
          <TabsTrigger value="translation">翻译设置</TabsTrigger>
          <TabsTrigger value="shortcuts">快捷键</TabsTrigger>
          <TabsTrigger value="advanced">高级设置</TabsTrigger>
        </TabsList>

        <TabsContent value="api" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API配置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>API提供者</Label>
                <Select value={providerType} onValueChange={setProviderType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="deepseek">DeepSeek</SelectItem>
                    <SelectItem value="claude">Claude</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>API密钥</Label>
                <Input
                  type="password"
                  value={currentConfig?.apiKey || ""}
                  onChange={(e) => handleConfigChange("apiKey", e.target.value)}
                  placeholder="请输入API密钥"
                />
              </div>

              <div className="space-y-2">
                <Label>API基础URL</Label>
                <Input
                  value={currentConfig?.apiBaseUrl || ""}
                  onChange={(e) =>
                    handleConfigChange("apiBaseUrl", e.target.value)
                  }
                  placeholder="API基础URL"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>视觉模型</Label>
                  <Input
                    value={currentConfig?.visionModel || ""}
                    onChange={(e) =>
                      handleConfigChange("visionModel", e.target.value)
                    }
                    placeholder="视觉模型名称"
                  />
                </div>
                <div className="space-y-2">
                  <Label>聊天模型</Label>
                  <Input
                    value={currentConfig?.chatModel || ""}
                    onChange={(e) =>
                      handleConfigChange("chatModel", e.target.value)
                    }
                    placeholder="聊天模型名称"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="translation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>翻译设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>样式保持程度</Label>
                <Slider
                  value={[styleLevel]}
                  onValueChange={([value]) => setStyleLevel(value)}
                  max={100}
                  step={1}
                  className="w-full"
                />
                <p className="text-sm text-muted-foreground">
                  控制翻译文本与原文字体的相似程度
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="shortcuts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>快捷键设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>切换翻译</Label>
                <Input
                  value={shortcuts.toggleTranslation}
                  onChange={(e) =>
                    setShortcuts({
                      ...shortcuts,
                      toggleTranslation: e.target.value,
                    })
                  }
                  placeholder="快捷键组合"
                />
              </div>
              <div className="space-y-2">
                <Label>翻译选中区域</Label>
                <Input
                  value={shortcuts.translateSelected}
                  onChange={(e) =>
                    setShortcuts({
                      ...shortcuts,
                      translateSelected: e.target.value,
                    })
                  }
                  placeholder="快捷键组合"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>高级设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>调试模式</Label>
                <Switch
                  checked={advancedSettings.debugMode}
                  onCheckedChange={(checked) =>
                    updateAdvancedSettings({ debugMode: checked })
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label>显示原文</Label>
                <Switch
                  checked={advancedSettings.showOriginalText}
                  onCheckedChange={(checked) =>
                    updateAdvancedSettings({ showOriginalText: checked })
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label>使用CORS代理</Label>
                <Switch
                  checked={advancedSettings.useCorsProxy}
                  onCheckedChange={(checked) =>
                    updateAdvancedSettings({ useCorsProxy: checked })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label>渲染类型</Label>
                <Select
                  value={advancedSettings.renderType}
                  onValueChange={(value: "overlay" | "replace") =>
                    updateAdvancedSettings({ renderType: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="overlay">覆盖模式</SelectItem>
                    <SelectItem value="replace">替换模式</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>数据管理</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button variant="destructive" onClick={clearHistory}>
                清除翻译历史
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
```

## 4. 性能优化

### 4.1 Web Worker 配置

```typescript
// src/workers/image-processor.worker.ts
import { detectTextAreas } from "@/api/ocr";

const ctx: Worker = self as any;

ctx.addEventListener("message", async (event) => {
  const { type, data } = event.data;

  try {
    switch (type) {
      case "DETECT_TEXT":
        const textAreas = await detectTextAreas(data.imageData);
        ctx.postMessage({ type: "DETECT_COMPLETE", result: textAreas });
        break;

      case "PREPROCESS_IMAGE":
        const processed = await preprocessImage(data.imageData);
        ctx.postMessage({ type: "PREPROCESS_COMPLETE", result: processed });
        break;

      default:
        ctx.postMessage({ type: "ERROR", error: "Unknown message type" });
    }
  } catch (error) {
    ctx.postMessage({ type: "ERROR", error: error.message });
  }
});

async function preprocessImage(imageData: string): Promise<string> {
  // 图像预处理逻辑
  return imageData;
}
```

### 4.2 缓存实现

```typescript
// src/utils/cache.ts
class SmartCache {
  private memoryCache = new Map<string, { data: any; timestamp: number }>();
  private maxMemorySize = 100;
  private maxAge = 24 * 60 * 60 * 1000; // 24小时

  async get(key: string): Promise<any | null> {
    const cached = this.memoryCache.get(key);

    if (cached) {
      if (Date.now() - cached.timestamp < this.maxAge) {
        return cached.data;
      } else {
        this.memoryCache.delete(key);
      }
    }

    return null;
  }

  async set(key: string, data: any): Promise<void> {
    // 清理过期数据
    this.cleanup();

    // 如果缓存已满，删除最旧的数据
    if (this.memoryCache.size >= this.maxMemorySize) {
      const oldestKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(oldestKey);
    }

    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, value] of this.memoryCache.entries()) {
      if (now - value.timestamp > this.maxAge) {
        this.memoryCache.delete(key);
      }
    }
  }

  clear(): void {
    this.memoryCache.clear();
  }
}

export const smartCache = new SmartCache();
```

## 5. 错误处理

### 5.1 错误边界组件

```typescript
// src/components/ErrorBoundary.tsx
import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Error caught by boundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800 flex items-center gap-2">
              <AlertTriangle className="w-5 h-5" />
              出现错误
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-red-600">
              {this.state.error?.message || "未知错误"}
            </p>
            <Button
              variant="outline"
              onClick={() => this.setState({ hasError: false, error: null })}
            >
              重试
            </Button>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}
```

### 5.2 错误处理 Hook

```typescript
// src/hooks/useErrorHandler.ts
import { useToast } from "@/components/ui/use-toast";

export function useErrorHandler() {
  const { toast } = useToast();

  const handleError = (error: Error, context?: string) => {
    console.error(`Error in ${context || "unknown context"}:`, error);

    let message = error.message;
    let title = "操作失败";

    if (error.message.includes("API key")) {
      title = "API配置错误";
      message = "请检查API密钥是否正确";
    } else if (error.message.includes("rate limit")) {
      title = "请求频率超限";
      message = "请稍后再试";
    } else if (error.message.includes("network")) {
      title = "网络错误";
      message = "请检查网络连接";
    }

    toast({
      title,
      description: message,
      variant: "destructive",
    });
  };

  return { handleError };
}
```

## 6. 开发工具

### 6.1 调试工具

```typescript
// src/utils/debug.ts
export const debugTools = {
  showState: () => {
    console.log("Translation State:", useTranslationStore.getState());
    console.log("Config State:", useConfigStore.getState());
  },

  clearCache: () => {
    smartCache.clear();
    console.log("Cache cleared");
  },

  simulateError: () => {
    throw new Error("Simulated error for testing");
  },

  testAPI: async () => {
    const config = useConfigStore.getState();
    const provider = config.providerConfig[config.providerType];

    try {
      // 测试API连接
      const response = await fetch(`${provider.apiBaseUrl}/models`, {
        headers: {
          Authorization: `Bearer ${provider.apiKey}`,
        },
      });

      if (response.ok) {
        console.log("API connection successful");
      } else {
        console.error("API connection failed:", response.status);
      }
    } catch (error) {
      console.error("API test failed:", error);
    }
  },
};

// 在开发模式下暴露调试工具
if (process.env.NODE_ENV === "development") {
  (window as any).__MANGA_TRANSLATOR_DEBUG__ = debugTools;
}
```

## 7. 构建配置

### 7.1 Vite 配置优化

```typescript
// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      input: {
        popup: resolve(__dirname, "src/popup.html"),
        options: resolve(__dirname, "src/options.html"),
        content: resolve(__dirname, "src/content/content.jsx"),
        background: resolve(__dirname, "src/background/background.js"),
      },
      output: {
        entryFileNames: "[name].js",
        chunkFileNames: "[name].js",
        assetFileNames: "[name].[ext]",
      },
    },
    target: "es2020",
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === "production",
      },
    },
  },
  define: {
    __DEV__: process.env.NODE_ENV === "development",
  },
});
```

## 8. 测试配置

### 8.1 单元测试

```typescript
// src/stores/__tests__/translation.test.ts
import { renderHook, act } from "@testing-library/react";
import { useTranslationStore } from "../translation";

describe("Translation Store", () => {
  beforeEach(() => {
    useTranslationStore.setState({
      enabled: false,
      mode: "manual",
      targetLanguage: "zh-CN",
      processing: false,
      currentImage: null,
      history: [],
    });
  });

  test("should set enabled state", () => {
    const { result } = renderHook(() => useTranslationStore());

    act(() => {
      result.current.setEnabled(true);
    });

    expect(result.current.enabled).toBe(true);
  });

  test("should add to history", () => {
    const { result } = renderHook(() => useTranslationStore());

    act(() => {
      result.current.addToHistory({
        imageUrl: "test.jpg",
        translatedText: "测试翻译",
      });
    });

    expect(result.current.history).toHaveLength(1);
    expect(result.current.history[0].translatedText).toBe("测试翻译");
  });
});
```

这个实施指南提供了详细的重构步骤和代码示例，可以帮助您逐步完成项目的现代化改造。每个步骤都包含了具体的代码实现，可以直接在项目中使用。
