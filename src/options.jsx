import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import OptionsApp from './components/Options/OptionsApp'
import { ThemeProvider } from './components/theme-provider'
import './index.css'

ReactDOM.createRoot(document.getElementById('app')).render(
  <React.StrictMode>
    <ThemeProvider defaultTheme="system" storageKey="manga-translator-theme">
      <OptionsApp />
    </ThemeProvider>
  </React.StrictMode>,
)
