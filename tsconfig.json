{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    /* Linting */
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noUncheckedIndexedAccess": true,
    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@/components/*": [
        "./src/components/*"
      ],
      "@/utils/*": [
        "./src/utils/*"
      ],
      "@/api/*": [
        "./src/api/*"
      ],
      "@/stores/*": [
        "./src/stores/*"
      ],
      "@/types/*": [
        "./src/types/*"
      ]
    },
    /* Chrome Extension specific */
    "types": [
      "chrome",
      "vite/client",
      "vitest/globals"
    ]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.js",
    "src/**/*.jsx"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "coverage",
    "src/test/**/*"
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}